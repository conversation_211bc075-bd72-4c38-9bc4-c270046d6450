<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

/**
 * \file    class/employerloan_installment.class.php
 * \ingroup employerloan
 * \brief   Classe pour la gestion des traites de prêt
 */

require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

/**
 * Classe pour la gestion des traites de prêt
 */
class EmployerLoanInstallment extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'employerloan_installment';

    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'employer_loan_installment';

    /**
     * @var string String with name of icon for myobject. Must be the part after the 'object_' into object_myobject.png
     */
    public $picto = 'payment';

    public $rowid;
    public $fk_loan;
    public $installment_number;
    public $due_date;
    public $amount_principal;
    public $amount_interest;
    public $amount_total;
    public $status; // pending, paid, overdue, cancelled
    public $date_payment;
    public $amount_paid;
    public $payment_method; // cash, bank_transfer, check
    public $fk_payment;
    public $note_private;
    public $note_public;
    public $date_creation;
    public $fk_user_creat;
    public $date_modification;
    public $fk_user_modif;

    // Statuts des traites
    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Constructor
     *
     * @param DoliDb $db Database handler
     */
    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Générer les traites pour un prêt
     *
     * @param int   $loanId        ID du prêt
     * @param float $loanAmount    Montant du prêt
     * @param float $interestRate  Taux d'intérêt annuel
     * @param int   $durationMonths Durée en mois
     * @param string $startDate    Date de début (Y-m-d)
     * @param User  $user          Utilisateur
     * @return array               Résultat de la génération
     */
    public function generateInstallments($loanId, $loanAmount, $interestRate, $durationMonths, $startDate, $user)
    {
        global $conf;

        $this->db->begin();

        try {
            // Supprimer les traites existantes si elles existent
            $this->deleteInstallments($loanId);

            // Calculer le montant mensuel
            $monthlyRate = $interestRate / 100 / 12;
            $monthlyPayment = $this->calculateMonthlyPayment($loanAmount, $monthlyRate, $durationMonths);

            $remainingPrincipal = $loanAmount;
            $installments = array();

            for ($i = 1; $i <= $durationMonths; $i++) {
                // Calculer la date d'échéance
                $dueDate = date('Y-m-d', strtotime($startDate . ' +' . $i . ' months'));

                // Calculer les intérêts pour ce mois
                $interestAmount = $remainingPrincipal * $monthlyRate;
                
                // Calculer le capital remboursé
                $principalAmount = $monthlyPayment - $interestAmount;
                
                // Ajustement pour la dernière traite
                if ($i == $durationMonths) {
                    $principalAmount = $remainingPrincipal;
                    $monthlyPayment = $principalAmount + $interestAmount;
                }

                // Créer la traite
                $installment = array(
                    'fk_loan' => $loanId,
                    'installment_number' => $i,
                    'due_date' => $dueDate,
                    'amount_principal' => round($principalAmount, 2),
                    'amount_interest' => round($interestAmount, 2),
                    'amount_total' => round($monthlyPayment, 2),
                    'status' => self::STATUS_PENDING,
                    'payment_method' => 'cash',
                    'date_creation' => dol_now(),
                    'fk_user_creat' => $user->id
                );

                $installmentId = $this->createInstallment($installment);
                if ($installmentId > 0) {
                    $installment['rowid'] = $installmentId;
                    $installments[] = $installment;
                    $remainingPrincipal -= $principalAmount;
                } else {
                    throw new Exception('Erreur création traite ' . $i);
                }
            }

            $this->db->commit();

            return array(
                'success' => true,
                'installments' => $installments,
                'monthly_payment' => round($monthlyPayment, 2),
                'total_interest' => round(($monthlyPayment * $durationMonths) - $loanAmount, 2)
            );

        } catch (Exception $e) {
            $this->db->rollback();
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Calculer le paiement mensuel
     *
     * @param float $principal    Capital
     * @param float $monthlyRate  Taux mensuel
     * @param int   $months       Nombre de mois
     * @return float              Paiement mensuel
     */
    private function calculateMonthlyPayment($principal, $monthlyRate, $months)
    {
        if ($monthlyRate == 0) {
            return $principal / $months;
        }

        return $principal * ($monthlyRate * pow(1 + $monthlyRate, $months)) / (pow(1 + $monthlyRate, $months) - 1);
    }

    /**
     * Créer une traite
     *
     * @param array $installment Données de la traite
     * @return int               ID de la traite créée
     */
    private function createInstallment($installment)
    {
        $sql = "INSERT INTO ".MAIN_DB_PREFIX.$this->table_element." (";
        $sql .= "fk_loan, installment_number, due_date, amount_principal, amount_interest, amount_total,";
        $sql .= "status, payment_method, date_creation, fk_user_creat";
        $sql .= ") VALUES (";
        $sql .= (int)$installment['fk_loan'].",";
        $sql .= (int)$installment['installment_number'].",";
        $sql .= "'".$installment['due_date']."',";
        $sql .= (float)$installment['amount_principal'].",";
        $sql .= (float)$installment['amount_interest'].",";
        $sql .= (float)$installment['amount_total'].",";
        $sql .= "'".$this->db->escape($installment['status'])."',";
        $sql .= "'".$this->db->escape($installment['payment_method'])."',";
        $sql .= "'".$this->db->idate($installment['date_creation'])."',";
        $sql .= (int)$installment['fk_user_creat'];
        $sql .= ")";

        if ($this->db->query($sql)) {
            return $this->db->last_insert_id(MAIN_DB_PREFIX.$this->table_element);
        }

        return -1;
    }

    /**
     * Supprimer les traites d'un prêt
     *
     * @param int $loanId ID du prêt
     * @return bool       Succès
     */
    public function deleteInstallments($loanId)
    {
        $sql = "DELETE FROM ".MAIN_DB_PREFIX.$this->table_element;
        $sql .= " WHERE fk_loan = ".(int)$loanId;
        $sql .= " AND status = '".self::STATUS_PENDING."'"; // Supprimer seulement les traites non payées

        return $this->db->query($sql);
    }

    /**
     * Obtenir les traites d'un prêt
     *
     * @param int $loanId ID du prêt
     * @return array      Traites
     */
    public function getInstallmentsByLoan($loanId)
    {
        $installments = array();

        $sql = "SELECT rowid, fk_loan, installment_number, due_date, amount_principal, amount_interest, amount_total,";
        $sql .= " status, date_payment, amount_paid, payment_method, note_private, note_public";
        $sql .= " FROM ".MAIN_DB_PREFIX.$this->table_element;
        $sql .= " WHERE fk_loan = ".(int)$loanId;
        $sql .= " ORDER BY installment_number";

        $resql = $this->db->query($sql);
        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $installments[] = array(
                    'rowid' => $obj->rowid,
                    'fk_loan' => $obj->fk_loan,
                    'installment_number' => $obj->installment_number,
                    'due_date' => $obj->due_date,
                    'amount_principal' => $obj->amount_principal,
                    'amount_interest' => $obj->amount_interest,
                    'amount_total' => $obj->amount_total,
                    'status' => $obj->status,
                    'date_payment' => $obj->date_payment,
                    'amount_paid' => $obj->amount_paid,
                    'payment_method' => $obj->payment_method,
                    'note_private' => $obj->note_private,
                    'note_public' => $obj->note_public
                );
            }
        }

        return $installments;
    }

    /**
     * Marquer une traite comme payée
     *
     * @param int    $installmentId ID de la traite
     * @param float  $amount        Montant payé
     * @param string $paymentMethod Méthode de paiement
     * @param User   $user          Utilisateur
     * @return bool                 Succès
     */
    public function markAsPaid($installmentId, $amount, $paymentMethod = 'cash', $user = null)
    {
        $sql = "UPDATE ".MAIN_DB_PREFIX.$this->table_element." SET";
        $sql .= " status = '".self::STATUS_PAID."',";
        $sql .= " date_payment = NOW(),";
        $sql .= " amount_paid = ".(float)$amount.",";
        $sql .= " payment_method = '".$this->db->escape($paymentMethod)."'";
        if ($user) {
            $sql .= ", date_modification = NOW(),";
            $sql .= " fk_user_modif = ".(int)$user->id;
        }
        $sql .= " WHERE rowid = ".(int)$installmentId;

        return $this->db->query($sql);
    }

    /**
     * Obtenir les traites en retard
     *
     * @param int $days Nombre de jours de retard
     * @return array    Traites en retard
     */
    public function getOverdueInstallments($days = 0)
    {
        $installments = array();

        $sql = "SELECT i.rowid, i.fk_loan, i.installment_number, i.due_date, i.amount_total,";
        $sql .= " l.ref as loan_ref, u.firstname, u.lastname";
        $sql .= " FROM ".MAIN_DB_PREFIX.$this->table_element." i";
        $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."employer_loan l ON i.fk_loan = l.rowid";
        $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user u ON l.fk_employee = u.rowid";
        $sql .= " WHERE i.status = '".self::STATUS_PENDING."'";
        $sql .= " AND i.due_date < DATE_SUB(NOW(), INTERVAL ".(int)$days." DAY)";
        $sql .= " ORDER BY i.due_date";

        $resql = $this->db->query($sql);
        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $installments[] = array(
                    'rowid' => $obj->rowid,
                    'fk_loan' => $obj->fk_loan,
                    'installment_number' => $obj->installment_number,
                    'due_date' => $obj->due_date,
                    'amount_total' => $obj->amount_total,
                    'loan_ref' => $obj->loan_ref,
                    'employee_name' => $obj->firstname . ' ' . $obj->lastname
                );
            }
        }

        return $installments;
    }

    /**
     * Mettre à jour le statut des traites en retard
     *
     * @return int Nombre de traites mises à jour
     */
    public function updateOverdueStatus()
    {
        $sql = "UPDATE ".MAIN_DB_PREFIX.$this->table_element." SET";
        $sql .= " status = '".self::STATUS_OVERDUE."'";
        $sql .= " WHERE status = '".self::STATUS_PENDING."'";
        $sql .= " AND due_date < CURDATE()";

        if ($this->db->query($sql)) {
            return $this->db->affected_rows($sql);
        }

        return 0;
    }

    /**
     * Obtenir le tableau d'amortissement
     *
     * @param int $loanId ID du prêt
     * @return array      Tableau d'amortissement
     */
    public function getAmortizationSchedule($loanId)
    {
        $schedule = array();
        $totalPrincipal = 0;
        $totalInterest = 0;
        $totalAmount = 0;

        $installments = $this->getInstallmentsByLoan($loanId);

        foreach ($installments as $installment) {
            $totalPrincipal += $installment['amount_principal'];
            $totalInterest += $installment['amount_interest'];
            $totalAmount += $installment['amount_total'];

            $schedule[] = array(
                'installment_number' => $installment['installment_number'],
                'due_date' => $installment['due_date'],
                'amount_principal' => $installment['amount_principal'],
                'amount_interest' => $installment['amount_interest'],
                'amount_total' => $installment['amount_total'],
                'remaining_principal' => $totalPrincipal,
                'status' => $installment['status'],
                'date_payment' => $installment['date_payment']
            );
        }

        return array(
            'schedule' => $schedule,
            'totals' => array(
                'principal' => $totalPrincipal,
                'interest' => $totalInterest,
                'total' => $totalAmount
            )
        );
    }

    /**
     * Obtenir les statistiques des paiements
     *
     * @param int $loanId ID du prêt
     * @return array      Statistiques
     */
    public function getPaymentStatistics($loanId)
    {
        $sql = "SELECT";
        $sql .= " COUNT(*) as total_installments,";
        $sql .= " SUM(CASE WHEN status = '".self::STATUS_PAID."' THEN 1 ELSE 0 END) as paid_installments,";
        $sql .= " SUM(CASE WHEN status = '".self::STATUS_OVERDUE."' THEN 1 ELSE 0 END) as overdue_installments,";
        $sql .= " SUM(amount_total) as total_amount,";
        $sql .= " SUM(CASE WHEN status = '".self::STATUS_PAID."' THEN amount_paid ELSE 0 END) as paid_amount,";
        $sql .= " SUM(CASE WHEN status IN ('".self::STATUS_PENDING."', '".self::STATUS_OVERDUE."') THEN amount_total ELSE 0 END) as remaining_amount";
        $sql .= " FROM ".MAIN_DB_PREFIX.$this->table_element;
        $sql .= " WHERE fk_loan = ".(int)$loanId;

        $resql = $this->db->query($sql);
        if ($resql && $this->db->num_rows($resql)) {
            $obj = $this->db->fetch_object($resql);
            
            return array(
                'total_installments' => $obj->total_installments,
                'paid_installments' => $obj->paid_installments,
                'overdue_installments' => $obj->overdue_installments,
                'pending_installments' => $obj->total_installments - $obj->paid_installments - $obj->overdue_installments,
                'total_amount' => $obj->total_amount,
                'paid_amount' => $obj->paid_amount,
                'remaining_amount' => $obj->remaining_amount,
                'completion_rate' => $obj->total_installments > 0 ? ($obj->paid_installments / $obj->total_installments) * 100 : 0
            );
        }

        return array();
    }

    /**
     * Return clicable name (with picto eventually)
     *
     * @param int $withpicto 0=No picto, 1=Include picto into link, 2=Only picto
     * @param string $option Where point the link ('card', 'nolink', ...)
     * @param int $notooltip 1=Disable tooltip
     * @param string $morecss Add more css on link
     * @param int $save_lastsearch_value -1=Auto, 0=No save of lastsearch_values when clicking, 1=Save lastsearch_values whenclicking
     * @return string HTML
     */
    public function getNomUrl($withpicto = 0, $option = '', $notooltip = 0, $morecss = '', $save_lastsearch_value = -1)
    {
        global $conf, $langs;

        if (!empty($conf->dol_no_mouse_hover)) {
            $notooltip = 1; // Force disable tooltips
        }

        $result = '';

        $label = img_picto('', $this->picto).' <u>'.$langs->trans("LoanInstallment").'</u>';
        $label .= '<br>';
        $label .= '<b>'.$langs->trans('Ref').':</b> '.$this->rowid.'<br>';
        $label .= '<b>'.$langs->trans('InstallmentNumber').':</b> '.$this->installment_number.'<br>';
        $label .= '<b>'.$langs->trans('DueDate').':</b> '.dol_print_date($this->due_date, 'day').'<br>';
        $label .= '<b>'.$langs->trans('Amount').':</b> '.price($this->amount_principal + $this->amount_interest).'<br>';

        $url = DOL_URL_ROOT.'/custom/employerloan/installment_card.php?id='.$this->rowid;

        if ($option != 'nolink') {
            // Add param to save lastsearch_values or not
            $add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
            if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
                $add_save_lastsearch_values = 1;
            }
            if ($add_save_lastsearch_values) {
                $url .= '&save_lastsearch_values=1';
            }
        }

        $linkclose = '';
        if (empty($notooltip)) {
            if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
                $label = $langs->trans("ShowInstallment");
                $linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
            }
            $linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
            $linkclose .= ' class="classfortooltip'.($morecss ? ' '.$morecss : '').'"';
        } else {
            $linkclose = ($morecss ? ' class="'.$morecss.'"' : '');
        }

        $linkstart = '<a href="'.$url.'"';
        $linkstart .= $linkclose.'>';
        $linkend = '</a>';

        $result .= $linkstart;
        if ($withpicto) {
            $result .= img_object(($notooltip ? '' : $label), $this->picto, ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
        }
        if ($withpicto != 2) {
            $result .= $this->installment_number;
        }
        $result .= $linkend;

        return $result;
    }

    /**
     * Return the status
     *
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function getLibStatut($mode = 0)
    {
        global $langs;

        $statusType = 'status4';
        $labelStatus = $langs->trans('Unpaid');
        $labelStatusShort = $langs->trans('Unpaid');

        if ($this->status == 1) {
            $statusType = 'status6';
            $labelStatus = $langs->trans('Paid');
            $labelStatusShort = $langs->trans('Paid');
        } elseif ($this->status == 2) {
            $statusType = 'status3';
            $labelStatus = $langs->trans('PartiallyPaid');
            $labelStatusShort = $langs->trans('PartiallyPaid');
        } elseif ($this->due_date < dol_now() && $this->status == 0) {
            $statusType = 'status8';
            $labelStatus = $langs->trans('Late');
            $labelStatusShort = $langs->trans('Late');
        }

        return dolGetStatus($labelStatus, $labelStatusShort, '', $statusType, $mode);
    }

    /**
     * Return the status
     *
     * @param int $status Id status
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        return $this->getLibStatut($mode);
    }
}
