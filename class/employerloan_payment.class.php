<?php
/**
 * Classe pour gérer les paiements de prêts employés
 */

require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

/**
 * Classe EmployerLoanPayment
 */
class EmployerLoanPayment extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'employer_loan_payment';

    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'employer_payment_loan';

    /**
     * @var int ID
     */
    public $id;

    /**
     * @var int Loan ID
     */
    public $fk_loan;

    /**
     * @var string Creation date
     */
    public $datec;

    /**
     * @var string Payment date
     */
    public $datep;

    /**
     * @var float Capital amount
     */
    public $amount_capital;

    /**
     * @var float Insurance amount
     */
    public $amount_insurance;

    /**
     * @var float Interest amount
     */
    public $amount_interest;

    /**
     * @var int Payment type ID
     */
    public $fk_typepayment;

    /**
     * @var string Payment number
     */
    public $num_payment;

    /**
     * @var string Private note
     */
    public $note_private;

    /**
     * @var string Public note
     */
    public $note_public;

    /**
     * @var int Bank ID
     */
    public $fk_bank;

    /**
     * @var int User creator ID
     */
    public $fk_user_creat;

    /**
     * @var int User modifier ID
     */
    public $fk_user_modif;

    /**
     * Constructor
     *
     * @param DoliDB $db Database handler
     */
    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Create payment in database
     *
     * @param User $user User that creates
     * @return int <0 if KO, Id of created object if OK
     */
    public function create($user)
    {
        global $conf, $langs;

        $error = 0;
        $now = dol_now();

        // Clean parameters
        if (isset($this->note_private)) {
            $this->note_private = trim($this->note_private);
        }
        if (isset($this->note_public)) {
            $this->note_public = trim($this->note_public);
        }
        if (isset($this->num_payment)) {
            $this->num_payment = trim($this->num_payment);
        }

        $this->db->begin();

        // Insert into employer_payment_loan
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_payment_loan";
        $sql .= " (fk_loan, datec, datep, amount_capital, amount_insurance, amount_interest,";
        $sql .= " fk_typepayment, num_payment, note_private, note_public, fk_bank, fk_user_creat)";
        $sql .= " VALUES (";
        $sql .= " ".(int) $this->fk_loan.",";
        $sql .= " '".$this->db->idate($now)."',";
        $sql .= " '".$this->db->idate($this->datep)."',";
        $sql .= " ".price2num($this->amount_capital).",";
        $sql .= " ".price2num($this->amount_insurance).",";
        $sql .= " ".price2num($this->amount_interest).",";
        $sql .= " ".(int) $this->fk_typepayment.",";
        $sql .= " '".$this->db->escape($this->num_payment)."',";
        $sql .= " '".$this->db->escape($this->note_private)."',";
        $sql .= " '".$this->db->escape($this->note_public)."',";
        $sql .= " ".(int) $this->fk_bank.",";
        $sql .= " ".(int) $user->id.")";

        $resql = $this->db->query($sql);
        if ($resql) {
            $this->id = $this->db->last_insert_id(MAIN_DB_PREFIX."employer_payment_loan");

            // Insert into bank table
            if (!$error && $this->fk_bank > 0) {
                $total_amount = $this->amount_capital + $this->amount_insurance + $this->amount_interest;
                
                $sql_bank = "INSERT INTO ".MAIN_DB_PREFIX."bank";
                $sql_bank .= " (datec, datev, dateo, amount, label, fk_account, fk_user_author,";
                $sql_bank .= " fk_type, num_chq, origin_id, origin_type)";
                $sql_bank .= " VALUES (";
                $sql_bank .= " '".$this->db->idate($now)."',";
                $sql_bank .= " '".$this->db->idate($this->datep)."',";
                $sql_bank .= " '".$this->db->idate($this->datep)."',";
                $sql_bank .= " ".(-$total_amount).",";  // Négatif car c'est un débit
                $sql_bank .= " 'Paiement échéance prêt employé',";
                $sql_bank .= " ".(int) $this->fk_bank.",";
                $sql_bank .= " ".(int) $user->id.",";
                $sql_bank .= " 'LCR',";  // Type LCR
                $sql_bank .= " '".$this->db->escape($this->num_payment)."',";
                $sql_bank .= " ".(int) $this->id.",";
                $sql_bank .= " 'employer_loan_payment')";

                $resql_bank = $this->db->query($sql_bank);
                if ($resql_bank) {
                    $bank_id = $this->db->last_insert_id(MAIN_DB_PREFIX."bank");
                    
                    // Update payment with bank ID
                    $sql_update = "UPDATE ".MAIN_DB_PREFIX."employer_payment_loan";
                    $sql_update .= " SET fk_bank = ".(int) $bank_id;
                    $sql_update .= " WHERE rowid = ".(int) $this->id;
                    
                    $this->db->query($sql_update);
                } else {
                    $error++;
                    $this->error = "Error inserting into bank: ".$this->db->lasterror();
                }
            }

            // Mettre à jour les tables de schedule et installment
            if (!$error) {
                $this->updateScheduleAndInstallments($user);
            }

            if (!$error) {
                $this->db->commit();
                return $this->id;
            } else {
                $this->db->rollback();
                return -1;
            }
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Load object in memory from the database
     *
     * @param int $id Id object
     * @return int <0 if KO, 0 if not found, >0 if OK
     */
    public function fetch($id)
    {
        $sql = "SELECT";
        $sql .= " t.rowid,";
        $sql .= " t.fk_loan,";
        $sql .= " t.datec,";
        $sql .= " t.datep,";
        $sql .= " t.amount_capital,";
        $sql .= " t.amount_insurance,";
        $sql .= " t.amount_interest,";
        $sql .= " t.fk_typepayment,";
        $sql .= " t.num_payment,";
        $sql .= " t.note_private,";
        $sql .= " t.note_public,";
        $sql .= " t.fk_bank,";
        $sql .= " t.fk_user_creat,";
        $sql .= " t.fk_user_modif";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_payment_loan as t";
        $sql .= " WHERE t.rowid = ".((int) $id);

        $resql = $this->db->query($sql);
        if ($resql) {
            $numrows = $this->db->num_rows($resql);
            if ($numrows) {
                $obj = $this->db->fetch_object($resql);

                $this->id = $obj->rowid;
                $this->fk_loan = $obj->fk_loan;
                $this->datec = $this->db->jdate($obj->datec);
                $this->datep = $this->db->jdate($obj->datep);
                $this->amount_capital = $obj->amount_capital;
                $this->amount_insurance = $obj->amount_insurance;
                $this->amount_interest = $obj->amount_interest;
                $this->fk_typepayment = $obj->fk_typepayment;
                $this->num_payment = $obj->num_payment;
                $this->note_private = $obj->note_private;
                $this->note_public = $obj->note_public;
                $this->fk_bank = $obj->fk_bank;
                $this->fk_user_creat = $obj->fk_user_creat;
                $this->fk_user_modif = $obj->fk_user_modif;

                $this->db->free($resql);

                return 1;
            } else {
                $this->db->free($resql);

                return 0;
            }
        } else {
            $this->error = "Error ".$this->db->lasterror();
            return -1;
        }
    }

    /**
     * Create payment for installment
     *
     * @param int $loan_id Loan ID
     * @param int $installment_id Installment ID
     * @param float $amount Amount to pay
     * @param int $bank_account Bank account ID
     * @param User $user User
     * @return int <0 if KO, >0 if OK
     */
    public function createInstallmentPayment($loan_id, $installment_id, $amount, $bank_account, $user)
    {
        global $conf;

        $this->fk_loan = $loan_id;
        $this->datep = dol_now();
        $this->amount_capital = $amount;
        $this->amount_insurance = 0;
        $this->amount_interest = 0;
        $this->fk_typepayment = 52; // LCR (rowid = 52 dans c_paiement)
        $this->num_payment = 'ECH'.sprintf('%06d', $installment_id);
        $this->fk_bank = $bank_account;
        $this->note_private = 'Paiement échéance #'.$installment_id;

        $result = $this->create($user);
        
        if ($result > 0) {
            // Mettre à jour le statut de l'échéance
            $sql_update_installment = "UPDATE ".MAIN_DB_PREFIX."employer_loan_installment";
            $sql_update_installment .= " SET status = 'paid', date_payment = NOW(), amount_paid = ".$amount.", fk_payment = ".$result;
            $sql_update_installment .= " WHERE rowid = ".(int) $installment_id;
            
            $this->db->query($sql_update_installment);
        }

        return $result;
    }

    /**
     * Update schedule and installment tables after payment
     *
     * @param User $user User
     * @return int <0 if KO, >0 if OK
     */
    private function updateScheduleAndInstallments($user)
    {
        global $conf;

        $error = 0;

        // 1. Mettre à jour employer_loan_schedule (si la table existe)
        $sql_check_schedule = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."employer_loan_schedule'";
        $resql_check_schedule = $this->db->query($sql_check_schedule);
        if ($resql_check_schedule && $this->db->num_rows($resql_check_schedule) > 0) {
            // Trouver la première échéance non entièrement payée
            $sql_find_schedule = "SELECT rowid, amount_capital, amount_paid FROM ".MAIN_DB_PREFIX."employer_loan_schedule";
            $sql_find_schedule .= " WHERE fk_loan = ".(int)$this->fk_loan;
            $sql_find_schedule .= " AND (amount_paid IS NULL OR amount_paid < amount_capital)";
            $sql_find_schedule .= " ORDER BY date_echeance ASC LIMIT 1";

            $resql_find_schedule = $this->db->query($sql_find_schedule);
            if ($resql_find_schedule && $this->db->num_rows($resql_find_schedule) > 0) {
                $obj_schedule = $this->db->fetch_object($resql_find_schedule);
                $new_amount_paid = ($obj_schedule->amount_paid ? $obj_schedule->amount_paid : 0) + $this->amount_capital;

                $sql_schedule = "UPDATE ".MAIN_DB_PREFIX."employer_loan_schedule";
                $sql_schedule .= " SET amount_paid = ".price2num($new_amount_paid);
                $sql_schedule .= " WHERE rowid = ".(int)$obj_schedule->rowid;

                $resql_schedule = $this->db->query($sql_schedule);
                if (!$resql_schedule) {
                    $error++;
                    $this->error = "Erreur mise à jour schedule: ".$this->db->lasterror();
                }
            }
        }

        // 2. Mettre à jour employer_loan_installment (si la table existe)
        if (!$error) {
            $sql_check_installment = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."employer_loan_installment'";
            $resql_check_installment = $this->db->query($sql_check_installment);
            if ($resql_check_installment && $this->db->num_rows($resql_check_installment) > 0) {
                // Trouver la première échéance non entièrement payée
                $sql_find_installment = "SELECT rowid, amount_total, amount_paid FROM ".MAIN_DB_PREFIX."employer_loan_installment";
                $sql_find_installment .= " WHERE fk_loan = ".(int)$this->fk_loan;
                $sql_find_installment .= " AND (amount_paid IS NULL OR amount_paid < amount_total)";
                $sql_find_installment .= " ORDER BY installment_number ASC LIMIT 1";

                $resql_find_installment = $this->db->query($sql_find_installment);
                if ($resql_find_installment && $this->db->num_rows($resql_find_installment) > 0) {
                    $obj_installment = $this->db->fetch_object($resql_find_installment);
                    $new_amount_paid = ($obj_installment->amount_paid ? $obj_installment->amount_paid : 0) + $this->amount_capital;

                    $sql_installment = "UPDATE ".MAIN_DB_PREFIX."employer_loan_installment";
                    $sql_installment .= " SET amount_paid = ".price2num($new_amount_paid);
                    $sql_installment .= " WHERE rowid = ".(int)$obj_installment->rowid;

                    $resql_installment = $this->db->query($sql_installment);
                    if (!$resql_installment) {
                        $error++;
                        $this->error = "Erreur mise à jour installment: ".$this->db->lasterror();
                    }
                }
            }
        }

        // 3. Vérifier si le prêt est entièrement payé
        if (!$error) {
            $sql_check = "SELECT capital, ";
            $sql_check .= " (SELECT COALESCE(SUM(amount_capital), 0) FROM ".MAIN_DB_PREFIX."employer_payment_loan WHERE fk_loan = l.rowid) as total_paid";
            $sql_check .= " FROM ".MAIN_DB_PREFIX."employer_loan l";
            $sql_check .= " WHERE l.rowid = ".(int)$this->fk_loan;

            $resql_check = $this->db->query($sql_check);
            if ($resql_check && $this->db->num_rows($resql_check) > 0) {
                $obj_check = $this->db->fetch_object($resql_check);

                // Convertir en nombres pour comparaison précise
                $capital = price2num($obj_check->capital);
                $total_paid = price2num($obj_check->total_paid);

                // Si le prêt est entièrement payé (avec tolérance de 0.01)
                if ($total_paid >= ($capital - 0.01)) {
                    $sql_loan = "UPDATE ".MAIN_DB_PREFIX."employer_loan";
                    $sql_loan .= " SET paid = 1, date_end_real = '".$this->db->idate($this->datep)."'";
                    $sql_loan .= " WHERE rowid = ".(int)$this->fk_loan;

                    $resql_loan = $this->db->query($sql_loan);
                    if (!$resql_loan) {
                        $error++;
                        $this->error = "Erreur mise à jour loan: ".$this->db->lasterror();
                    }
                }
            }
        }

        return $error ? -1 : 1;
    }
}
