# Corrections des permissions - <PERSON><PERSON><PERSON> EmployerLoan

## Problème identifié

L'erreur "Accès refusé" dans les pages `list.php` et `card.php` était causée par des incohérences dans les vérifications de permissions. Le code utilisait parfois `loan` au lieu de `employerloan` dans les vérifications de droits.

## Corrections effectuées

### 1. Fichier `list.php`
- **Ligne 64** : `restrictedArea($user, 'loan', ...)` → `restrictedArea($user, 'employerloan', ...)`
- **Ligne 241** : `$user->rights->loan->write` → `$user->rights->employerloan->write`

### 2. Fichier `card.php`
- **Ligne 167** : `restrictedArea($user, 'loan', ...)` → `restrictedArea($user, 'employerloan', ...)`
- **Ligne 187** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **<PERSON>gne 198** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 211** : `$user->hasRight('loan', 'read')` → `$user->hasRight('employerloan', 'read')`
- **Ligne 308** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 369** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 416** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 424** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 632-633** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 638** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 970** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 975** : `$user->hasRight('loan', 'read')` → `$user->hasRight('employerloan', 'read')`
- **Ligne 980** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 985** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **Ligne 990** : `$user->rights->loan->delete` → `$user->rights->employerloan->delete`

### 3. Autres fichiers corrigés
- **payment/payment.php** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`
- **payment_installment.php** : `$user->hasRight('loan', 'write')` → `$user->hasRight('employerloan', 'write')`

## Permissions définies dans le module

D'après le fichier `core/modules/modEmployerLoan.class.php`, les permissions sont :

- **ID 180001** : `employerloan->read` - "Lire les crédits salariés"
- **ID 180002** : `employerloan->write` - "Créer/modifier les crédits salariés"  
- **ID 180003** : `employerloan->delete` - "Supprimer les crédits salariés"

## Solution pour l'utilisateur

### Étape 1 : Vérifier les permissions
Exécuter le script de test : `http://votre-dolibarr/custom/employerloan/test_permissions.php`

### Étape 2 : Attribuer les permissions
1. Se connecter en tant qu'administrateur
2. Aller dans **Accueil → Utilisateurs**
3. Modifier l'utilisateur `germanetti`
4. Dans l'onglet **Permissions**, rechercher "Crédits salariés" ou "EmployerLoan"
5. Cocher les permissions nécessaires :
   - ☑️ Lire les crédits salariés
   - ☑️ Créer/modifier les crédits salariés
   - ☑️ Supprimer les crédits salariés (optionnel)
6. Sauvegarder
7. L'utilisateur doit se déconnecter et se reconnecter

### Étape 3 : Vider le cache (optionnel)
Si le problème persiste :
1. Vider le cache Dolibarr (menu Outils → Purger cache)
2. Effacer les cookies du navigateur
3. Se reconnecter

## Test final

Après avoir attribué les permissions, tester l'accès aux pages :
- `http://votre-dolibarr/custom/employerloan/list.php`
- `http://votre-dolibarr/custom/employerloan/card.php`

## Notes techniques

- Le module utilise le nom `employerloan` pour les permissions, pas `loan`
- La fonction `restrictedArea()` doit utiliser `employerloan` comme premier paramètre
- Les méthodes `hasRight()` doivent utiliser `employerloan` comme premier paramètre
- Les propriétés `$user->rights->employerloan->*` sont les bonnes références
