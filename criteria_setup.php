<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * \file    criteria_setup.php
 * \ingroup employerloan
 * \brief   Setup page for loan evaluation criteria
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
dol_include_once('/employerloan/class/employerloan_criteria.class.php');

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "admin"));

// Get parameters
$action = GETPOST('action', 'aZ09');
$id = GETPOST('id', 'int');
$confirm = GETPOST('confirm', 'alpha');

// Security check
if (!$user->admin) {
    accessforbidden();
}

// Initialize technical objects
$object = new EmployerLoanCriteria($db);

/*
 * Actions
 */

if ($action == 'add' && GETPOST('cancel', 'alpha') == '') {
    $error = 0;
    
    $object->code = GETPOST('code', 'alpha');
    $object->label = GETPOST('label', 'alpha');
    $object->description = GETPOST('description', 'alpha');
    $object->weight = GETPOST('weight', 'numeric');
    $object->min_value = GETPOST('min_value', 'numeric');
    $object->max_value = GETPOST('max_value', 'numeric');
    $object->formula = GETPOST('formula', 'alpha');
    $object->active = GETPOST('active', 'int');
    $object->position = GETPOST('position', 'int');
    $object->entity = $conf->entity;
    $object->date_creation = dol_now();
    $object->fk_user_creat = $user->id;
    
    if (empty($object->code)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Code")), null, 'errors');
        $error++;
    }
    if (empty($object->label)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Label")), null, 'errors');
        $error++;
    }
    if (empty($object->weight) || $object->weight <= 0) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Weight")), null, 'errors');
        $error++;
    }
    
    if (!$error) {
        $result = $object->create($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaCreated"), null, 'mesgs');
            $action = '';
        } else {
            setEventMessages($object->error, $object->errors, 'errors');
            $action = 'create';
        }
    } else {
        $action = 'create';
    }
}

if ($action == 'update' && GETPOST('cancel', 'alpha') == '') {
    $error = 0;
    
    if ($object->fetch($id) > 0) {
        $object->code = GETPOST('code', 'alpha');
        $object->label = GETPOST('label', 'alpha');
        $object->description = GETPOST('description', 'alpha');
        $object->weight = GETPOST('weight', 'numeric');
        $object->min_value = GETPOST('min_value', 'numeric');
        $object->max_value = GETPOST('max_value', 'numeric');
        $object->formula = GETPOST('formula', 'alpha');
        $object->active = GETPOST('active', 'int');
        $object->position = GETPOST('position', 'int');
        $object->date_modification = dol_now();
        $object->fk_user_modif = $user->id;
        
        if (empty($object->code)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Code")), null, 'errors');
            $error++;
        }
        if (empty($object->label)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Label")), null, 'errors');
            $error++;
        }
        if (empty($object->weight) || $object->weight <= 0) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Weight")), null, 'errors');
            $error++;
        }
        
        if (!$error) {
            $result = $object->update($user);
            if ($result > 0) {
                setEventMessages($langs->trans("CriteriaUpdated"), null, 'mesgs');
                $action = '';
            } else {
                setEventMessages($object->error, $object->errors, 'errors');
                $action = 'edit';
            }
        } else {
            $action = 'edit';
        }
    }
}

if ($action == 'confirm_delete' && $confirm == 'yes') {
    if ($object->fetch($id) > 0) {
        $result = $object->delete($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaDeleted"), null, 'mesgs');
            $action = '';
        } else {
            setEventMessages($object->error, $object->errors, 'errors');
        }
    }
}

if ($action == 'activate') {
    if ($object->fetch($id) > 0) {
        $object->active = 1;
        $result = $object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaActivated"), null, 'mesgs');
        }
    }
}

if ($action == 'disable') {
    if ($object->fetch($id) > 0) {
        $object->active = 0;
        $result = $object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaDisabled"), null, 'mesgs');
        }
    }
}

/*
 * View
 */

$form = new Form($db);

$title = $langs->trans('LoanEvaluationCriteria');
$help_url = '';

llxHeader('', $title, $help_url);

$linkback = '<a href="'.dol_buildpath('/employerloan/admin/employerloan.php', 1).'">'.$langs->trans("BackToModuleList").'</a>';
print load_fiche_titre($title, $linkback, 'title_setup');

// Configuration header
$head = array();
$head[0][0] = dol_buildpath('/employerloan/admin/employerloan.php', 1);
$head[0][1] = $langs->trans('Setup');
$head[0][2] = 'setup';

$head[1][0] = dol_buildpath('/employerloan/criteria_setup.php', 1);
$head[1][1] = $langs->trans('EvaluationCriteria');
$head[1][2] = 'criteria';

$head[2][0] = dol_buildpath('/employerloan/committee_setup.php', 1);
$head[2][1] = $langs->trans('LoanCommittee');
$head[2][2] = 'committee';

print dol_get_fiche_head($head, 'criteria', '', -1);

// Buttons
if ($action != 'create' && $action != 'edit') {
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?action=create">'.$langs->trans('NewCriteria').'</a>';
    print '</div>';
}

// Form to create/edit criteria
if ($action == 'create' || $action == 'edit') {
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="'.($action == 'create' ? 'add' : 'update').'">';
    if ($action == 'edit') {
        print '<input type="hidden" name="id" value="'.$id.'">';
        $object->fetch($id);
    }
    
    print '<table class="border centpercent">';
    
    // Code
    print '<tr><td class="fieldrequired">'.$langs->trans('Code').'</td>';
    print '<td><input type="text" name="code" size="20" maxlength="50" value="'.($action == 'edit' ? $object->code : GETPOST('code', 'alpha')).'" required></td></tr>';
    
    // Label
    print '<tr><td class="fieldrequired">'.$langs->trans('Label').'</td>';
    print '<td><input type="text" name="label" size="50" maxlength="255" value="'.($action == 'edit' ? $object->label : GETPOST('label', 'alpha')).'" required></td></tr>';
    
    // Description
    print '<tr><td>'.$langs->trans('Description').'</td>';
    print '<td><textarea name="description" rows="3" cols="50">'.($action == 'edit' ? $object->description : GETPOST('description', 'alpha')).'</textarea></td></tr>';
    
    // Weight
    print '<tr><td class="fieldrequired">'.$langs->trans('Weight').'</td>';
    print '<td><input type="number" name="weight" step="0.1" min="0.1" max="10" value="'.($action == 'edit' ? $object->weight : (GETPOST('weight', 'numeric') ? GETPOST('weight', 'numeric') : '1.0')).'" required></td></tr>';
    
    // Min value
    print '<tr><td>'.$langs->trans('MinValue').'</td>';
    print '<td><input type="number" name="min_value" step="0.01" value="'.($action == 'edit' ? $object->min_value : GETPOST('min_value', 'numeric')).'"></td></tr>';
    
    // Max value
    print '<tr><td>'.$langs->trans('MaxValue').'</td>';
    print '<td><input type="number" name="max_value" step="0.01" value="'.($action == 'edit' ? $object->max_value : GETPOST('max_value', 'numeric')).'"></td></tr>';
    
    // Formula
    print '<tr><td>'.$langs->trans('Formula').'</td>';
    print '<td><textarea name="formula" rows="2" cols="50" placeholder="Ex: (salary - existing_loans) / requested_amount">'.($action == 'edit' ? $object->formula : GETPOST('formula', 'alpha')).'</textarea></td></tr>';
    
    // Active
    print '<tr><td>'.$langs->trans('Active').'</td>';
    print '<td>'.$form->selectyesno('active', ($action == 'edit' ? $object->active : 1), 1).'</td></tr>';
    
    // Position
    print '<tr><td>'.$langs->trans('Position').'</td>';
    print '<td><input type="number" name="position" min="0" value="'.($action == 'edit' ? $object->position : GETPOST('position', 'int')).'"></td></tr>';
    
    print '</table>';
    
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.($action == 'create' ? $langs->trans('Create') : $langs->trans('Modify')).'">';
    print ' &nbsp; ';
    print '<input type="submit" class="button button-cancel" name="cancel" value="'.$langs->trans('Cancel').'">';
    print '</div>';
    
    print '</form>';
}

// List of criteria
if ($action != 'create' && $action != 'edit') {
    $sql = "SELECT rowid, code, label, description, weight, min_value, max_value, active, position";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_criteria";
    $sql .= " WHERE entity = ".$conf->entity;
    $sql .= " ORDER BY position ASC, code ASC";
    
    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);
        
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th>'.$langs->trans('Code').'</th>';
        print '<th>'.$langs->trans('Label').'</th>';
        print '<th>'.$langs->trans('Weight').'</th>';
        print '<th>'.$langs->trans('MinValue').'</th>';
        print '<th>'.$langs->trans('MaxValue').'</th>';
        print '<th>'.$langs->trans('Status').'</th>';
        print '<th>'.$langs->trans('Position').'</th>';
        print '<th width="60">'.$langs->trans('Action').'</th>';
        print '</tr>';
        
        if ($num > 0) {
            $i = 0;
            while ($i < $num) {
                $obj = $db->fetch_object($resql);
                
                print '<tr class="oddeven">';
                print '<td><strong>'.$obj->code.'</strong></td>';
                print '<td>'.$obj->label.'</td>';
                print '<td class="center">'.$obj->weight.'</td>';
                print '<td class="center">'.($obj->min_value ? $obj->min_value : '-').'</td>';
                print '<td class="center">'.($obj->max_value ? $obj->max_value : '-').'</td>';
                print '<td class="center">';
                if ($obj->active) {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?action=disable&id='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Enabled"), 'switch_on');
                    print '</a>';
                } else {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?action=activate&id='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Disabled"), 'switch_off');
                    print '</a>';
                }
                print '</td>';
                print '<td class="center">'.$obj->position.'</td>';
                print '<td class="center">';
                print '<a href="'.$_SERVER["PHP_SELF"].'?action=edit&id='.$obj->rowid.'">';
                print img_edit();
                print '</a>';
                print ' ';
                print '<a href="'.$_SERVER["PHP_SELF"].'?action=delete&id='.$obj->rowid.'&token='.newToken().'" onclick="return confirm(\''.$langs->trans('ConfirmDeleteCriteria').'\');">';
                print img_delete();
                print '</a>';
                print '</td>';
                print '</tr>';
                
                $i++;
            }
        } else {
            print '<tr><td colspan="8"><span class="opacitymedium">'.$langs->trans("NoCriteriaFound").'</span></td></tr>';
        }
        
        print '</table>';
        
        $db->free($resql);
    } else {
        dol_print_error($db);
    }
}

print dol_get_fiche_end();

// End of page
llxFooter();
