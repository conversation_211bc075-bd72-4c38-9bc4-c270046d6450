<?php
/**
 * Script de diagnostic pour list.php du module employerloan
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan"));

$title = "Diagnostic list.php - EmployerLoan";
llxHeader("", $title);

print '<h1>'.$title.'</h1>';

print '<h2>1. Vérification du module</h2>';

// Vérifier si le module est activé
if (isModEnabled('employerloan')) {
    print '<p>✅ Module employerloan activé</p>';
} else {
    print '<p>❌ Module employerloan non activé</p>';
    print '<p><strong>Solution :</strong> Aller dans Configuration → Modules et activer le module "Crédits salariés"</p>';
}

print '<h2>2. Vérification des permissions utilisateur</h2>';

print '<p><strong>Utilisateur :</strong> '.$user->login.'</p>';

if (isset($user->rights->employerloan)) {
    print '<p>✅ Droits employerloan définis</p>';
    
    if (isset($user->rights->employerloan->read) && $user->rights->employerloan->read) {
        print '<p>✅ Droit de lecture : OUI</p>';
    } else {
        print '<p>❌ Droit de lecture : NON</p>';
        print '<p><strong>Solution :</strong> Attribuer le droit "Lire les crédits salariés" à l\'utilisateur</p>';
    }
} else {
    print '<p>❌ Droits employerloan non définis</p>';
    print '<p><strong>Solution :</strong> Attribuer les droits employerloan à l\'utilisateur</p>';
}

print '<h2>3. Test de restrictedArea</h2>';

try {
    $result = restrictedArea($user, 'employerloan', '', '', '');
    print '<p>✅ restrictedArea(employerloan) : OK</p>';
} catch (Exception $e) {
    print '<p>❌ restrictedArea(employerloan) : '.$e->getMessage().'</p>';
}

print '<h2>4. Vérification de la classe EmployerLoan</h2>';

// Inclure la classe
dol_include_once('/employerloan/class/employerloan.class.php');

if (class_exists('EmployerLoan')) {
    print '<p>✅ Classe EmployerLoan trouvée</p>';
    
    try {
        $loan_test = new EmployerLoan($db);
        print '<p>✅ Instanciation de EmployerLoan : OK</p>';
    } catch (Exception $e) {
        print '<p>❌ Erreur lors de l\'instanciation : '.$e->getMessage().'</p>';
    }
} else {
    print '<p>❌ Classe EmployerLoan non trouvée</p>';
    print '<p><strong>Vérifier :</strong> /custom/employerloan/class/employerloan.class.php</p>';
}

print '<h2>5. Vérification de la table de base de données</h2>';

$sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."employer_loan'";
$resql = $db->query($sql);
if ($resql && $db->num_rows($resql) > 0) {
    print '<p>✅ Table employer_loan existe</p>';
    
    // Compter les enregistrements
    $sql_count = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."employer_loan WHERE entity = ".$conf->entity;
    $resql_count = $db->query($sql_count);
    if ($resql_count) {
        $obj = $db->fetch_object($resql_count);
        print '<p>📊 Nombre d\'enregistrements : '.$obj->nb.'</p>';
    }
} else {
    print '<p>❌ Table employer_loan n\'existe pas</p>';
    print '<p><strong>Solution :</strong> Exécuter le script SQL de création des tables</p>';
}

print '<h2>6. Test de la requête SQL de list.php</h2>';

$sqlfields = "l.rowid, l.label, l.capital, l.datestart, l.dateend, l.paid, l.fk_employee, u.lastname, u.firstname";
$sql = "SELECT ".$sqlfields;
$sql .= " FROM ".MAIN_DB_PREFIX."employer_loan as l";
$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user as u ON l.fk_employee = u.rowid";
$sql .= " WHERE l.entity = ".$conf->entity;
$sql .= " LIMIT 1";

print '<p><strong>Requête testée :</strong></p>';
print '<pre>'.htmlspecialchars($sql).'</pre>';

$resql = $db->query($sql);
if ($resql) {
    print '<p>✅ Requête SQL : OK</p>';
    $num = $db->num_rows($resql);
    print '<p>📊 Résultats trouvés : '.$num.'</p>';
} else {
    print '<p>❌ Erreur SQL : '.$db->lasterror().'</p>';
}

print '<h2>7. Test d\'accès direct à list.php</h2>';

if (isModEnabled('employerloan') && isset($user->rights->employerloan->read) && $user->rights->employerloan->read) {
    print '<p>✅ Conditions d\'accès remplies</p>';
    print '<p><a href="list.php" target="_blank">🔗 Tester list.php</a></p>';
} else {
    print '<p>❌ Conditions d\'accès non remplies</p>';
    print '<p>Corriger les problèmes ci-dessus avant de tester list.php</p>';
}

print '<h2>8. Actions recommandées</h2>';

$actions = array();

if (!isModEnabled('employerloan')) {
    $actions[] = "Activer le module employerloan dans Configuration → Modules";
}

if (!isset($user->rights->employerloan->read) || !$user->rights->employerloan->read) {
    $actions[] = "Attribuer les droits employerloan à l'utilisateur ".$user->login;
}

if (empty($actions)) {
    print '<p>✅ <strong>Aucune action requise</strong> - Tous les tests sont OK</p>';
} else {
    print '<ol>';
    foreach ($actions as $action) {
        print '<li>'.$action.'</li>';
    }
    print '</ol>';
}

llxFooter();
