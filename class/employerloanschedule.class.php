<?php
require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

/**
 *		Class to manage Schedule of employer loans
 */
class EmployerLoanSchedule extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'employer_loan_schedule';

    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'employer_loan_schedule';

    /**
     * @var int ID
     */
    public $rowid;

    /**
     * @var int Loan ID
     */
    public $fk_loan;

    /**
     * @var string Create date
     */
    public $datec;
    public $tms;

    /**
     * @var string Payment date
     */
    public $datep;

    public $amounts = array(); // Array of amounts
    public $amount_capital; // Total amount of payment
    public $amount_insurance;
    public $amount_interest;

    /**
     * @var int Payment Type ID
     */
    public $fk_typepayment;

    /**
     * @var int Payment ID
     */
    public $num_payment;

    /**
     * @var int Bank ID
     */
    public $fk_bank;

    /**
     * @var int Loan Payment ID
     */
    public $fk_payment_loan;

    /**
     * @var int Bank ID
     */
    public $fk_user_creat;

    /**
     * @var int User ID
     */
    public $fk_user_modif;

    /**
     * @var EmployerLoanSchedule[]
     * @see EmployerLoanSchedule::fetchAll()
     */
    public $lines = array();

    /**
     * @deprecated
     * @see $amount, $amounts
     */
    public $total;

    public $type_code;
    public $type_label;

    public $note_private;
    public $note_public;

    public function __construct($db)
    {
        $this->db = $db;
    }

    public function fetch($id)
    {
        $sql = "SELECT * FROM ".MAIN_DB_PREFIX."employer_loan_schedule WHERE rowid = ".((int) $id);
        $resql = $this->db->query($sql);
        if ($resql) {
            if ($this->db->num_rows($resql)) {
                $obj = $this->db->fetch_object($resql);
                foreach ($obj as $key => $value) {
                    $this->$key = $value;
                }
                $this->db->free($resql);
                return 1;
            }
            $this->db->free($resql);
            return 0;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     *  Create payment of loan into database.
     *  Use this->amounts to have list of lines for the payment
     *
     *  @param      User		$user   User making payment
     *  @return     int     			<0 if KO, id of payment if OK
     */
    public function create($user)
    {
        global $conf, $langs;

        $error = 0;
        $now = dol_now();

        // Validate parameters
        if (!$this->datep) {
            $this->error = 'ErrorBadValueForParameter';
            return -1;
        }

        // Clean parameters
        if (isset($this->fk_loan)) {
            $this->fk_loan = (int) $this->fk_loan;
        }
        if (isset($this->amount_capital)) {
            $this->amount_capital = trim($this->amount_capital ? $this->amount_capital : 0);
        }
        if (isset($this->amount_insurance)) {
            $this->amount_insurance = trim($this->amount_insurance ? $this->amount_insurance : 0);
        }
        if (isset($this->amount_interest)) {
            $this->amount_interest = trim($this->amount_interest ? $this->amount_interest : 0);
        }

        $this->db->begin();

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_schedule (";
        $sql .= "fk_loan,";
        $sql .= "datec,";
        $sql .= "datep,";
        $sql .= "amount_capital,";
        $sql .= "amount_insurance,";
        $sql .= "amount_interest,";
        $sql .= "fk_typepayment,";
        $sql .= "num_payment,";
        $sql .= "note_private,";
        $sql .= "note_public,";
        $sql .= "fk_bank,";
        $sql .= "fk_payment_loan,";
        $sql .= "fk_user_creat";
        $sql .= ") VALUES (";
        $sql .= ((int) $this->fk_loan).",";
        $sql .= "'".$this->db->idate($now)."',";
        $sql .= "'".$this->db->idate($this->datep)."',";
        $sql .= price2num($this->amount_capital).",";
        $sql .= price2num($this->amount_insurance).",";
        $sql .= price2num($this->amount_interest).",";
        $sql .= ((int) $this->fk_typepayment).",";
        $sql .= "'".$this->db->escape($this->num_payment)."',";
        $sql .= "'".$this->db->escape($this->note_private)."',";
        $sql .= "'".$this->db->escape($this->note_public)."',";
        $sql .= ((int) $this->fk_bank).",";
        $sql .= ($this->fk_payment_loan ? ((int) $this->fk_payment_loan) : 'NULL').",";
        $sql .= ((int) $user->id);
        $sql .= ")";

        dol_syslog(get_class($this)."::create", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->rowid = $this->db->last_insert_id(MAIN_DB_PREFIX."employer_loan_schedule");
            $this->id = $this->rowid;

            if (!$error) {
                $this->db->commit();
                return $this->rowid;
            } else {
                $this->db->rollback();
                return -1;
            }
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    public function update($user)
    {
        $this->db->begin();
        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan_schedule SET ";
        $sql .= "fk_loan=".((int) $this->fk_loan).", ";
        $sql .= "datep='".$this->db->idate($this->datep)."', ";
        $sql .= "amount_capital=".price2num($this->amount_capital).", ";
        $sql .= "amount_insurance=".price2num($this->amount_insurance).", ";
        $sql .= "amount_interest=".price2num($this->amount_interest).", ";
        $sql .= "fk_typepayment=".((int) $this->fk_typepayment).", ";
        $sql .= "num_payment='".$this->db->escape($this->num_payment)."', ";
        $sql .= "note_private='".$this->db->escape($this->note_private)."', ";
        $sql .= "note_public='".$this->db->escape($this->note_public)."', ";
        $sql .= "fk_bank=".((int) $this->fk_bank).", ";
        $sql .= "fk_payment_loan=".((int) $this->fk_payment_loan).", ";
        $sql .= "fk_user_modif=".((int) $user->id)." ";
        $sql .= "WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    public function delete($user)
    {
        $this->db->begin();
        $sql = "DELETE FROM ".MAIN_DB_PREFIX."employer_loan_schedule WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     *  Load all object in memory from database
     *
     *  @param	int		$loanid     Id object
     *  @return int         		<0 if KO, >0 if OK
     */
    public function fetchAll($loanid)
    {
        global $langs;

        $sql = "SELECT";
        $sql .= " t.rowid,";
        $sql .= " t.fk_loan,";
        $sql .= " t.datec,";
        $sql .= " t.tms,";
        $sql .= " t.datep,";
        $sql .= " t.amount_capital,";
        $sql .= " t.amount_insurance,";
        $sql .= " t.amount_interest,";
        $sql .= " t.fk_typepayment,";
        $sql .= " t.num_payment,";
        $sql .= " t.note_private,";
        $sql .= " t.note_public,";
        $sql .= " t.fk_bank,";
        $sql .= " t.fk_payment_loan,";
        $sql .= " t.fk_user_creat,";
        $sql .= " t.fk_user_modif";
        $sql .= " FROM ".MAIN_DB_PREFIX.$this->table_element." as t";
        $sql .= " WHERE t.fk_loan = ".((int) $loanid);
        $sql .= " ORDER BY t.datep";

        dol_syslog(get_class($this)."::fetchAll", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $num = $this->db->num_rows($resql);
            $i = 0;
            while ($i < $num) {
                $obj = $this->db->fetch_object($resql);

                $line = new EmployerLoanSchedule($this->db);
                $line->rowid = $obj->rowid;
                $line->id = $obj->rowid;
                $line->fk_loan = $obj->fk_loan;
                $line->datec = $this->db->jdate($obj->datec);
                $line->tms = $this->db->jdate($obj->tms);
                $line->datep = $this->db->jdate($obj->datep);
                $line->amount_capital = $obj->amount_capital;
                $line->amount_insurance = $obj->amount_insurance;
                $line->amount_interest = $obj->amount_interest;
                $line->fk_typepayment = $obj->fk_typepayment;
                $line->num_payment = $obj->num_payment;
                $line->note_private = $obj->note_private;
                $line->note_public = $obj->note_public;
                $line->fk_bank = $obj->fk_bank;
                $line->fk_payment_loan = $obj->fk_payment_loan;
                $line->fk_user_creat = $obj->fk_user_creat;
                $line->fk_user_modif = $obj->fk_user_modif;

                $this->lines[$i] = $line;

                $i++;
            }
            $this->db->free($resql);

            return 1;
        } else {
            $this->error = "Error ".$this->db->lasterror();
            return -1;
        }
    }

    /**
     * Calculate Monthly Payments
     *
     * @param   double  $capital        Capital
     * @param   double  $rate           rate
     * @param   int     $nbterm         nb term
     * @return  double                  mensuality
     */
    public function calcMonthlyPayments($capital, $rate, $nbterm)
    {
        $result = '';

        if (!empty($capital) && !empty($rate) && !empty($nbterm)) {
            $result = ($capital * ($rate / 12)) / (1 - pow((1 + ($rate / 12)), ($nbterm * -1)));
        }

        return $result;
    }

    /**
     * Return clicable name (with picto eventually)
     *
     * @param int $withpicto 0=No picto, 1=Include picto into link, 2=Only picto
     * @param string $option Where point the link ('card', 'nolink', ...)
     * @param int $notooltip 1=Disable tooltip
     * @param string $morecss Add more css on link
     * @param int $save_lastsearch_value -1=Auto, 0=No save of lastsearch_values when clicking, 1=Save lastsearch_values whenclicking
     * @return string HTML
     */
    public function getNomUrl($withpicto = 0, $option = '', $notooltip = 0, $morecss = '', $save_lastsearch_value = -1)
    {
        global $conf, $langs, $hookmanager;

        if (!empty($conf->dol_no_mouse_hover)) {
            $notooltip = 1; // Force disable tooltips
        }

        $result = '';

        $label = img_picto('', 'payment').' <u>'.$langs->trans("LoanSchedule").'</u>';
        $label .= '<br>';
        $label .= '<b>'.$langs->trans('Ref').':</b> '.$this->rowid.'<br>';
        $label .= '<b>'.$langs->trans('Date').':</b> '.dol_print_date($this->datep, 'day').'<br>';
        $label .= '<b>'.$langs->trans('Amount').':</b> '.price($this->amount_capital + $this->amount_interest + $this->amount_insurance).'<br>';

        $url = DOL_URL_ROOT.'/custom/employerloan/schedule.php?loanid='.$this->fk_loan.'#line_'.$this->rowid;

        if ($option != 'nolink') {
            // Add param to save lastsearch_values or not
            $add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
            if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
                $add_save_lastsearch_values = 1;
            }
            if ($add_save_lastsearch_values) {
                $url .= '&save_lastsearch_values=1';
            }
        }

        $linkclose = '';
        if (empty($notooltip)) {
            if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
                $label = $langs->trans("ShowSchedule");
                $linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
            }
            $linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
            $linkclose .= ' class="classfortooltip'.($morecss ? ' '.$morecss : '').'"';
        } else {
            $linkclose = ($morecss ? ' class="'.$morecss.'"' : '');
        }

        $linkstart = '<a href="'.$url.'"';
        $linkstart .= $linkclose.'>';
        $linkend = '</a>';

        $result .= $linkstart;
        if ($withpicto) {
            $result .= img_object(($notooltip ? '' : $label), 'payment', ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
        }
        if ($withpicto != 2) {
            $result .= $this->rowid;
        }
        $result .= $linkend;

        return $result;
    }

    /**
     * Return the status
     *
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function getLibStatut($mode = 0)
    {
        global $langs;

        $statusType = 'status4';
        $labelStatus = $langs->trans('Scheduled');
        $labelStatusShort = $langs->trans('Scheduled');

        if ($this->fk_payment_loan > 0) {
            $statusType = 'status6';
            $labelStatus = $langs->trans('Paid');
            $labelStatusShort = $langs->trans('Paid');
        }

        return dolGetStatus($labelStatus, $labelStatusShort, '', $statusType, $mode);
    }

    /**
     * Return the status
     *
     * @param int $status Id status
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        return $this->getLibStatut($mode);
    }
}