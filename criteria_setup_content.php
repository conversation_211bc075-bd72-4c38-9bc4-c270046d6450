<?php
/* Copyright (C) 2024 NextGestion
 *
 * Content for criteria setup tab in admin page
 */

if (!defined('NOREQUIRESOC')) {
    define('NOREQUIRESOC', '1');
}
if (!defined('NOREQUIRETRAN')) {
    define('NOREQUIRETRAN', '1');
}
if (!defined('NOCSRFCHECK')) {
    define('NOCSRFCHECK', '1');
}
if (!defined('NOTOKENRENEWAL')) {
    define('NOTOKENRENEWAL', '1');
}
if (!defined('NOREQUIREMENU')) {
    define('NOREQUIREMENU', '1');
}
if (!defined('NOREQUIREHTML')) {
    define('NOREQUIREHTML', '1');
}
if (!defined('NOREQUIREAJAX')) {
    define('NOREQUIREAJAX', '1');
}

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
dol_include_once('/employerloan/class/employerloan_criteria.class.php');

// Get parameters for criteria
$action_criteria = GETPOST('action_criteria', 'aZ09');
$id_criteria = GETPOST('id_criteria', 'int');
$confirm_criteria = GETPOST('confirm_criteria', 'alpha');

// Initialize technical objects
$criteria_object = new EmployerLoanCriteria($db);

/*
 * Actions for criteria
 */

if ($action_criteria == 'add' && GETPOST('cancel_criteria', 'alpha') == '') {
    $error = 0;
    
    $criteria_object->code = GETPOST('code', 'alpha');
    $criteria_object->label = GETPOST('label', 'alpha');
    $criteria_object->description = GETPOST('description', 'alpha');
    $criteria_object->weight = GETPOST('weight', 'numeric');
    $criteria_object->min_value = GETPOST('min_value', 'numeric');
    $criteria_object->max_value = GETPOST('max_value', 'numeric');
    $criteria_object->formula = GETPOST('formula', 'alpha');
    $criteria_object->active = GETPOST('active', 'int');
    $criteria_object->position = GETPOST('position', 'int');
    $criteria_object->entity = $conf->entity;
    $criteria_object->date_creation = dol_now();
    $criteria_object->fk_user_creat = $user->id;
    
    if (empty($criteria_object->code)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Code")), null, 'errors');
        $error++;
    }
    if (empty($criteria_object->label)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Label")), null, 'errors');
        $error++;
    }
    if (empty($criteria_object->weight) || $criteria_object->weight <= 0) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Weight")), null, 'errors');
        $error++;
    }
    
    if (!$error) {
        $result = $criteria_object->create($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaCreated"), null, 'mesgs');
            $action_criteria = '';
        } else {
            setEventMessages($criteria_object->error, $criteria_object->errors, 'errors');
            $action_criteria = 'create';
        }
    } else {
        $action_criteria = 'create';
    }
}

if ($action_criteria == 'update' && GETPOST('cancel_criteria', 'alpha') == '') {
    $error = 0;
    
    if ($criteria_object->fetch($id_criteria) > 0) {
        $criteria_object->code = GETPOST('code', 'alpha');
        $criteria_object->label = GETPOST('label', 'alpha');
        $criteria_object->description = GETPOST('description', 'alpha');
        $criteria_object->weight = GETPOST('weight', 'numeric');
        $criteria_object->min_value = GETPOST('min_value', 'numeric');
        $criteria_object->max_value = GETPOST('max_value', 'numeric');
        $criteria_object->formula = GETPOST('formula', 'alpha');
        $criteria_object->active = GETPOST('active', 'int');
        $criteria_object->position = GETPOST('position', 'int');
        $criteria_object->date_modification = dol_now();
        $criteria_object->fk_user_modif = $user->id;
        
        if (empty($criteria_object->code)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Code")), null, 'errors');
            $error++;
        }
        if (empty($criteria_object->label)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Label")), null, 'errors');
            $error++;
        }
        if (empty($criteria_object->weight) || $criteria_object->weight <= 0) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Weight")), null, 'errors');
            $error++;
        }
        
        if (!$error) {
            $result = $criteria_object->update($user);
            if ($result > 0) {
                setEventMessages($langs->trans("CriteriaUpdated"), null, 'mesgs');
                $action_criteria = '';
            } else {
                setEventMessages($criteria_object->error, $criteria_object->errors, 'errors');
                $action_criteria = 'edit';
            }
        } else {
            $action_criteria = 'edit';
        }
    }
}

if ($action_criteria == 'confirm_delete' && $confirm_criteria == 'yes') {
    if ($criteria_object->fetch($id_criteria) > 0) {
        $result = $criteria_object->delete($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaDeleted"), null, 'mesgs');
            $action_criteria = '';
        } else {
            setEventMessages($criteria_object->error, $criteria_object->errors, 'errors');
        }
    }
}

if ($action_criteria == 'activate') {
    if ($criteria_object->fetch($id_criteria) > 0) {
        $criteria_object->active = 1;
        $result = $criteria_object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaActivated"), null, 'mesgs');
        }
    }
}

if ($action_criteria == 'disable') {
    if ($criteria_object->fetch($id_criteria) > 0) {
        $criteria_object->active = 0;
        $result = $criteria_object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CriteriaDisabled"), null, 'mesgs');
        }
    }
}

/*
 * View for criteria
 */

// Buttons
if ($action_criteria != 'create' && $action_criteria != 'edit') {
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?tab=criteria&action_criteria=create">'.$langs->trans('NewCriteria').'</a>';
    print '</div>';
}

// Form to create/edit criteria
if ($action_criteria == 'create' || $action_criteria == 'edit') {
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'?tab=criteria">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action_criteria" value="'.($action_criteria == 'create' ? 'add' : 'update').'">';
    if ($action_criteria == 'edit') {
        print '<input type="hidden" name="id_criteria" value="'.$id_criteria.'">';
        $criteria_object->fetch($id_criteria);
    }
    
    print '<table class="border centpercent">';
    
    // Code
    print '<tr><td class="fieldrequired">'.$langs->trans('Code').'</td>';
    print '<td><input type="text" name="code" size="20" maxlength="50" value="'.($action_criteria == 'edit' ? $criteria_object->code : GETPOST('code', 'alpha')).'" required></td></tr>';
    
    // Label
    print '<tr><td class="fieldrequired">'.$langs->trans('Label').'</td>';
    print '<td><input type="text" name="label" size="50" maxlength="255" value="'.($action_criteria == 'edit' ? $criteria_object->label : GETPOST('label', 'alpha')).'" required></td></tr>';
    
    // Description
    print '<tr><td>'.$langs->trans('Description').'</td>';
    print '<td><textarea name="description" rows="3" cols="50">'.($action_criteria == 'edit' ? $criteria_object->description : GETPOST('description', 'alpha')).'</textarea></td></tr>';
    
    // Weight
    print '<tr><td class="fieldrequired">'.$langs->trans('Weight').'</td>';
    print '<td><input type="number" name="weight" step="0.1" min="0.1" max="10" value="'.($action_criteria == 'edit' ? $criteria_object->weight : (GETPOST('weight', 'numeric') ? GETPOST('weight', 'numeric') : '1.0')).'" required></td></tr>';
    
    // Min value
    print '<tr><td>'.$langs->trans('MinValue').'</td>';
    print '<td><input type="number" name="min_value" step="0.01" value="'.($action_criteria == 'edit' ? $criteria_object->min_value : GETPOST('min_value', 'numeric')).'"></td></tr>';
    
    // Max value
    print '<tr><td>'.$langs->trans('MaxValue').'</td>';
    print '<td><input type="number" name="max_value" step="0.01" value="'.($action_criteria == 'edit' ? $criteria_object->max_value : GETPOST('max_value', 'numeric')).'"></td></tr>';
    
    // Formula
    print '<tr><td>'.$langs->trans('Formula').'</td>';
    print '<td><textarea name="formula" rows="2" cols="50" placeholder="Ex: (salary - existing_loans) / requested_amount">'.($action_criteria == 'edit' ? $criteria_object->formula : GETPOST('formula', 'alpha')).'</textarea></td></tr>';
    
    // Active
    print '<tr><td>'.$langs->trans('Active').'</td>';
    print '<td>'.$form->selectyesno('active', ($action_criteria == 'edit' ? $criteria_object->active : 1), 1).'</td></tr>';
    
    // Position
    print '<tr><td>'.$langs->trans('Position').'</td>';
    print '<td><input type="number" name="position" min="0" value="'.($action_criteria == 'edit' ? $criteria_object->position : GETPOST('position', 'int')).'"></td></tr>';
    
    print '</table>';
    
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.($action_criteria == 'create' ? $langs->trans('Create') : $langs->trans('Modify')).'">';
    print ' &nbsp; ';
    print '<input type="submit" class="button button-cancel" name="cancel_criteria" value="'.$langs->trans('Cancel').'">';
    print '</div>';
    
    print '</form>';
}

// List of criteria
if ($action_criteria != 'create' && $action_criteria != 'edit') {
    $sql = "SELECT rowid, code, label, description, weight, min_value, max_value, active, position";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_criteria";
    $sql .= " WHERE entity = ".$conf->entity;
    $sql .= " ORDER BY position ASC, code ASC";
    
    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);
        
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th>'.$langs->trans('Code').'</th>';
        print '<th>'.$langs->trans('Label').'</th>';
        print '<th>'.$langs->trans('Weight').'</th>';
        print '<th>'.$langs->trans('MinValue').'</th>';
        print '<th>'.$langs->trans('MaxValue').'</th>';
        print '<th>'.$langs->trans('Status').'</th>';
        print '<th>'.$langs->trans('Position').'</th>';
        print '<th width="60">'.$langs->trans('Action').'</th>';
        print '</tr>';
        
        if ($num > 0) {
            $i = 0;
            while ($i < $num) {
                $obj = $db->fetch_object($resql);
                
                print '<tr class="oddeven">';
                print '<td><strong>'.$obj->code.'</strong></td>';
                print '<td>'.$obj->label.'</td>';
                print '<td class="center">'.$obj->weight.'</td>';
                print '<td class="center">'.($obj->min_value ? $obj->min_value : '-').'</td>';
                print '<td class="center">'.($obj->max_value ? $obj->max_value : '-').'</td>';
                print '<td class="center">';
                if ($obj->active) {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?tab=criteria&action_criteria=disable&id_criteria='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Enabled"), 'switch_on');
                    print '</a>';
                } else {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?tab=criteria&action_criteria=activate&id_criteria='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Disabled"), 'switch_off');
                    print '</a>';
                }
                print '</td>';
                print '<td class="center">'.$obj->position.'</td>';
                print '<td class="center">';
                print '<a href="'.$_SERVER["PHP_SELF"].'?tab=criteria&action_criteria=edit&id_criteria='.$obj->rowid.'">';
                print img_edit();
                print '</a>';
                print ' ';
                print '<a href="'.$_SERVER["PHP_SELF"].'?tab=criteria&action_criteria=delete&id_criteria='.$obj->rowid.'&token='.newToken().'" onclick="return confirm(\''.$langs->trans('ConfirmDeleteCriteria').'\');">';
                print img_delete();
                print '</a>';
                print '</td>';
                print '</tr>';
                
                $i++;
            }
        } else {
            print '<tr><td colspan="8"><span class="opacitymedium">'.$langs->trans("NoCriteriaFound").'</span></td></tr>';
        }
        
        print '</table>';
        
        $db->free($resql);
    } else {
        dol_print_error($db);
    }
}
