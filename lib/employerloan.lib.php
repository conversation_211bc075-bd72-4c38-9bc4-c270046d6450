<?php
/**
 *      \file       lib/employerloan.lib.php
 *      \ingroup    employerloan
 *      \brief      Library for employerloan module
 */

/**
 * Prepare array with list of tabs (alias for compatibility with loan module)
 *
 * @param   Object  $object     Object related to tabs
 * @return  array               Array of tabs to show
 */
function loan_prepare_head($object)
{
    return employerloan_prepare_head($object);
}

/**
 * Prepare array with list of tabs
 *
 * @param   Object  $object     Object related to tabs
 * @return  array               Array of tabs to show
 */
function employerloan_prepare_head($object)
{
    global $db, $langs, $conf;
    $tab = 0;
    $head = array();
    $head[$tab][0] = DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id;
    $head[$tab][1] = $langs->trans('Card');
    $head[$tab][2] = 'card';
    $tab++;
    $head[$tab][0] = DOL_URL_ROOT.'/custom/employerloan/schedule.php?loanid='.$object->id;
    $head[$tab][1] = $langs->trans('FinancialCommitment');
    $head[$tab][2] = 'FinancialCommitment';
    $tab++;
    require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
    require_once DOL_DOCUMENT_ROOT.'/core/class/link.class.php';
    $upload_dir = $conf->employerloan->dir_output."/".dol_sanitizeFileName($object->ref);
    $nbFiles = count(dol_dir_list($upload_dir, 'files', 0, '', '(\.meta|_preview.*\.png)$'));
    $nbLinks = class_exists('Link') ? Link::count($db, $object->element, $object->id) : 0;
    $head[$tab][0] = DOL_URL_ROOT.'/custom/employerloan/document.php?id='.$object->id;
    $head[$tab][1] = $langs->trans("Documents");
    if (($nbFiles + $nbLinks) > 0) {
        $head[$tab][1] .= '<span class="badge marginleftonlyshort">'.($nbFiles + $nbLinks).'</span>';
    }
    $head[$tab][2] = 'documents';
    $tab++;
    if (empty($conf->global->MAIN_DISABLE_NOTES_TAB)) {
        $nbNote = (empty($object->note_private) ? 0 : 1) + (empty($object->note_public) ? 0 : 1);
        $head[$tab][0] = DOL_URL_ROOT."/custom/employerloan/note.php?id=".$object->id;
        $head[$tab][1] = $langs->trans("Notes");
        if ($nbNote > 0) {
            $head[$tab][1] .= '<span class="badge marginleftonlyshort">'.$nbNote.'</span>';
        }
        $head[$tab][2] = 'note';
        $tab++;
    }
    $head[$tab][0] = DOL_URL_ROOT.'/custom/employerloan/info.php?id='.$object->id;
    $head[$tab][1] = $langs->trans("Info");
    $head[$tab][2] = 'info';
    $tab++;
    return $head;
}

/**
 * Prepare array of tabs for EmployerLoanRequest
 *
 * @param	EmployerLoanRequest	$object		EmployerLoanRequest
 * @return 	array						Array of tabs
 */
function employerloan_request_prepare_head($object)
{
    global $db, $langs, $conf;

    $langs->load("employerloan@employerloan");

    $h = 0;
    $head = array();

    $head[$h][0] = dol_buildpath("/custom/employerloan/loan_request_card.php", 1).'?id='.$object->id;
    $head[$h][1] = $langs->trans("Card");
    $head[$h][2] = 'card';
    $h++;

    // Show evaluation tab if request has been evaluated
    if ($object->evaluation_score > 0) {
        $head[$h][0] = dol_buildpath('/custom/employerloan/loan_request_evaluation.php', 1).'?id='.$object->id;
        $head[$h][1] = $langs->trans('Evaluation');
        $head[$h][2] = 'evaluation';
        $h++;
    }

    // Show committee tab if request is being evaluated
    if ($object->status >= $object::STATUS_EVALUATING) {
        $head[$h][0] = dol_buildpath('/custom/employerloan/loan_request_committee.php', 1).'?id='.$object->id;
        $head[$h][1] = $langs->trans('Committee');
        $head[$h][2] = 'committee';
        $h++;
    }

    // Show more tabs from modules
    // Attributes
    if (isset($object->fields['note_public']) || isset($object->fields['note_private'])) {
        $head[$h][0] = dol_buildpath('/employerloan/loan_request_note.php', 1).'?id='.$object->id;
        $head[$h][1] = $langs->trans('Notes');
        $head[$h][2] = 'note';
        $h++;
    }

    require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
    require_once DOL_DOCUMENT_ROOT.'/core/class/link.class.php';
    $upload_dir = $conf->employerloan->dir_output."/loan_request/".dol_sanitizeFileName($object->ref);
    $nbFiles = count(dol_dir_list($upload_dir, 'files', 0, '', '(\.meta|_preview.*\.png)$'));
    $nbLinks = Link::count($db, $object->element, $object->id);
    $head[$h][0] = dol_buildpath("/employerloan/loan_request_document.php", 1).'?id='.$object->id;
    $head[$h][1] = $langs->trans('Documents');
    if (($nbFiles + $nbLinks) > 0) {
        $head[$h][1] .= '<span class="badge marginleftonlyshort">'.($nbFiles + $nbLinks).'</span>';
    }
    $head[$h][2] = 'document';
    $h++;

    $head[$h][0] = dol_buildpath("/employerloan/loan_request_agenda.php", 1).'?id='.$object->id;
    $head[$h][1] = $langs->trans("Events");
    $head[$h][2] = 'agenda';
    $h++;

    return $head;
}

/**
 * Get employee loan history
 *
 * @param int $employeeId Employee ID
 * @param int $limit      Limit number of records
 * @return array          History records
 */
function getEmployeeLoanHistory($employeeId, $limit = 50)
{
    global $db;

    $history = array();

    $sql = "SELECT h.rowid, h.event_type, h.event_date, h.amount, h.description,";
    $sql .= " lr.ref as request_ref, l.ref as loan_ref,";
    $sql .= " u.firstname, u.lastname";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_history h";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."employer_loan_request lr ON h.fk_loan_request = lr.rowid";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."employer_loan l ON h.fk_loan = l.rowid";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user u ON h.fk_user_action = u.rowid";
    $sql .= " WHERE h.fk_employee = ".(int)$employeeId;
    $sql .= " ORDER BY h.event_date DESC";
    if ($limit > 0) {
        $sql .= " LIMIT ".(int)$limit;
    }

    $resql = $db->query($sql);
    if ($resql) {
        while ($obj = $db->fetch_object($resql)) {
            $history[] = array(
                'rowid' => $obj->rowid,
                'event_type' => $obj->event_type,
                'event_date' => $obj->event_date,
                'amount' => $obj->amount,
                'description' => $obj->description,
                'request_ref' => $obj->request_ref,
                'loan_ref' => $obj->loan_ref,
                'user_name' => $obj->firstname.' '.$obj->lastname
            );
        }
    }

    return $history;
}

/**
 * Get employee loan statistics
 *
 * @param int $employeeId Employee ID
 * @return array          Statistics
 */
function getEmployeeLoanStatistics($employeeId)
{
    global $db;

    $stats = array(
        'total_requests' => 0,
        'approved_requests' => 0,
        'rejected_requests' => 0,
        'active_loans' => 0,
        'total_borrowed' => 0,
        'total_repaid' => 0,
        'outstanding_balance' => 0
    );

    // Statistiques des demandes
    $sql = "SELECT status, COUNT(*) as count, SUM(amount_requested) as total_amount";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_request";
    $sql .= " WHERE fk_employee = ".(int)$employeeId;
    $sql .= " GROUP BY status";

    $resql = $db->query($sql);
    if ($resql) {
        while ($obj = $db->fetch_object($resql)) {
            $stats['total_requests'] += $obj->count;
            if ($obj->status == 2) { // Approved
                $stats['approved_requests'] = $obj->count;
            } elseif ($obj->status == 3) { // Rejected
                $stats['rejected_requests'] = $obj->count;
            }
        }
    }

    // Statistiques des prêts
    $sql = "SELECT COUNT(*) as active_loans, SUM(amount) as total_borrowed";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan";
    $sql .= " WHERE fk_employee = ".(int)$employeeId;
    $sql .= " AND status = 'active'";

    $resql = $db->query($sql);
    if ($resql && $db->num_rows($resql)) {
        $obj = $db->fetch_object($resql);
        $stats['active_loans'] = $obj->active_loans;
        $stats['total_borrowed'] = $obj->total_borrowed;
    }

    // Montant remboursé
    $sql = "SELECT SUM(i.amount_paid) as total_repaid";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_installment i";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."employer_loan l ON i.fk_loan = l.rowid";
    $sql .= " WHERE l.fk_employee = ".(int)$employeeId;
    $sql .= " AND i.status = 'paid'";

    $resql = $db->query($sql);
    if ($resql && $db->num_rows($resql)) {
        $obj = $db->fetch_object($resql);
        $stats['total_repaid'] = $obj->total_repaid ?: 0;
    }

    $stats['outstanding_balance'] = $stats['total_borrowed'] - $stats['total_repaid'];

    return $stats;
}

