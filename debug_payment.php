<?php
/**
 * Debug du problème de paiement
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_payment.class.php';

// Vérification des permissions
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'alpha');

print '<html><head><title>Debug paiement</title></head><body>';
print '<h1>Debug du problème de paiement</h1>';

if ($action == 'debug') {
    print '<h2>Diagnostic en cours...</h2>';
    
    // 1. Vérifier la classe EmployerLoanPayment
    print '<h3>1. Vérification de la classe EmployerLoanPayment</h3>';
    
    if (class_exists('EmployerLoanPayment')) {
        print "✅ Classe EmployerLoanPayment existe<br>";
        
        $payment = new EmployerLoanPayment($db);
        
        if (method_exists($payment, 'create')) {
            print "✅ Méthode create() existe<br>";
        } else {
            print "❌ Méthode create() manquante<br>";
        }
        
        if (method_exists($payment, 'createInstallmentPayment')) {
            print "✅ Méthode createInstallmentPayment() existe<br>";
        } else {
            print "❌ Méthode createInstallmentPayment() manquante<br>";
        }
        
    } else {
        print "❌ Classe EmployerLoanPayment n'existe pas<br>";
    }
    
    // 2. Vérifier les tables
    print '<h3>2. Vérification des tables</h3>';
    
    $tables = ['employer_payment_loan', 'bank'];
    foreach ($tables as $table) {
        $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
        $resql = $db->query($sql);
        if ($resql && $db->num_rows($resql) > 0) {
            print "✅ Table ".$table." existe<br>";
        } else {
            print "❌ Table ".$table." n'existe pas<br>";
        }
    }
    
    // 3. Vérifier la structure de employer_payment_loan
    print '<h3>3. Structure de employer_payment_loan</h3>';
    
    $sql_desc = "DESCRIBE ".MAIN_DB_PREFIX."employer_payment_loan";
    $resql_desc = $db->query($sql_desc);
    if ($resql_desc) {
        print '<table border="1" style="border-collapse: collapse;">';
        print '<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>';
        while ($obj = $db->fetch_object($resql_desc)) {
            print '<tr>';
            print '<td>'.$obj->Field.'</td>';
            print '<td>'.$obj->Type.'</td>';
            print '<td>'.$obj->Null.'</td>';
            print '<td>'.$obj->Key.'</td>';
            print '<td>'.$obj->Default.'</td>';
            print '</tr>';
        }
        print '</table>';
    }
    
    // 4. Test de création de paiement
    print '<h3>4. Test de création de paiement</h3>';
    
    // Créer un prêt de test
    $loan = new EmployerLoan($db);
    $loan->label = 'Test paiement - À SUPPRIMER';
    $loan->capital = 1200;
    $loan->datestart = dol_now();
    $loan->dateend = dol_time_plus_duree(dol_now(), 3, 'm');
    $loan->nbterm = 3;
    $loan->rate = 0;
    $loan->fk_employee = $user->id;
    $loan->paid = 0;
    $loan->entity = $conf->entity;
    $loan->accountancy_account_capital = '*********';
    $loan->accountancy_account_interest = '*********';
    $loan->accountancy_account_insurance = '*********';
    
    $loan_result = $loan->create($user);
    if ($loan_result > 0) {
        print "✅ Prêt de test créé (ID: ".$loan_result.")<br>";
        
        // Tester la création de paiement
        try {
            $payment = new EmployerLoanPayment($db);
            $payment->fk_loan = $loan_result;
            $payment->datep = dol_now();
            $payment->amount_capital = 400;
            $payment->amount_insurance = 0;
            $payment->amount_interest = 0;
            $payment->fk_typepayment = 52; // LCR
            $payment->num_payment = 'TEST001';
            $payment->note_private = 'Test de paiement';
            $payment->note_public = '';
            $payment->fk_bank = 1; // Compte par défaut
            
            print "Tentative de création de paiement...<br>";
            $payment_result = $payment->create($user);
            
            if ($payment_result > 0) {
                print "✅ Paiement créé avec succès (ID: ".$payment_result.")<br>";
                
                // Vérifier l'insertion dans employer_payment_loan
                $sql_check = "SELECT * FROM ".MAIN_DB_PREFIX."employer_payment_loan WHERE rowid = ".$payment_result;
                $resql_check = $db->query($sql_check);
                if ($resql_check && $db->num_rows($resql_check) > 0) {
                    print "✅ Enregistrement trouvé dans employer_payment_loan<br>";
                    $obj_payment = $db->fetch_object($resql_check);
                    print "- Montant capital : ".$obj_payment->amount_capital."<br>";
                    print "- Type paiement : ".$obj_payment->fk_typepayment."<br>";
                    print "- Compte bancaire : ".$obj_payment->fk_bank."<br>";
                } else {
                    print "❌ Aucun enregistrement dans employer_payment_loan<br>";
                }
                
                // Vérifier l'insertion dans bank
                $sql_bank = "SELECT * FROM ".MAIN_DB_PREFIX."bank WHERE origin_id = ".$payment_result." AND origin_type = 'employer_loan_payment'";
                $resql_bank = $db->query($sql_bank);
                if ($resql_bank && $db->num_rows($resql_bank) > 0) {
                    print "✅ Enregistrement trouvé dans bank<br>";
                    $obj_bank = $db->fetch_object($resql_bank);
                    print "- Montant : ".$obj_bank->amount."<br>";
                    print "- Type : ".$obj_bank->fk_type."<br>";
                    print "- Compte : ".$obj_bank->fk_account."<br>";
                } else {
                    print "❌ Aucun enregistrement dans bank<br>";
                }
                
            } else {
                print "❌ Erreur création paiement : ".$payment->error."<br>";
                if (is_array($payment->errors)) {
                    foreach ($payment->errors as $error) {
                        print "- ".$error."<br>";
                    }
                }
            }
            
        } catch (Exception $e) {
            print "❌ Exception lors de la création : ".$e->getMessage()."<br>";
        }
        
        // Nettoyage
        $loan->delete($user);
        print "✅ Prêt de test supprimé<br>";
        
    } else {
        print "❌ Erreur création prêt de test<br>";
    }
    
    // 5. Vérifier le code de payment.php
    print '<h3>5. Vérification du code de payment.php</h3>';
    
    $payment_file = DOL_DOCUMENT_ROOT.'/custom/employerloan/payment/payment.php';
    if (file_exists($payment_file)) {
        $content = file_get_contents($payment_file);
        
        if (strpos($content, 'add_payment') !== false) {
            print "✅ Action add_payment présente<br>";
        } else {
            print "❌ Action add_payment manquante<br>";
        }
        
        if (strpos($content, 'EmployerLoanPayment') !== false) {
            print "✅ Classe EmployerLoanPayment utilisée<br>";
        } else {
            print "❌ Classe EmployerLoanPayment non utilisée<br>";
        }
        
        if (strpos($content, '->create($user)') !== false) {
            print "✅ Appel à create() présent<br>";
        } else {
            print "❌ Appel à create() manquant<br>";
        }
        
        if (strpos($content, 'PaymentRecorded') !== false) {
            print "✅ Message de succès présent<br>";
        } else {
            print "❌ Message de succès manquant<br>";
        }
        
    } else {
        print "❌ Fichier payment.php non trouvé<br>";
    }
    
    // 6. Vérifier les permissions
    print '<h3>6. Vérification des permissions</h3>';
    
    if ($user->hasRight('loan', 'write')) {
        print "✅ Utilisateur a les droits d'écriture sur les prêts<br>";
    } else {
        print "❌ Utilisateur n'a pas les droits d'écriture sur les prêts<br>";
    }
    
    // 7. Recommandations
    print '<h3>7. Recommandations de débogage</h3>';
    
    print '<div style="background: #fff3cd; color: #856404; padding: 10px; border: 1px solid #ffeaa7; border-radius: 3px;">';
    print '<h4>Pour déboguer le problème :</h4>';
    print '<ol>';
    print '<li>Activez le mode debug Dolibarr dans conf.php</li>';
    print '<li>Vérifiez les logs d\'erreur PHP</li>';
    print '<li>Testez manuellement le formulaire de paiement</li>';
    print '<li>Vérifiez que tous les champs requis sont remplis</li>';
    print '<li>Vérifiez les permissions utilisateur</li>';
    print '</ol>';
    print '</div>';
    
} else {
    print '<p>Ce script va diagnostiquer pourquoi les paiements ne s\'enregistrent pas.</p>';
    
    print '<h3>Problème rapporté :</h3>';
    print '<ul>';
    print '<li>❌ Aucune insertion dans la table bank</li>';
    print '<li>❌ Aucune insertion dans la table employer_payment_loan</li>';
    print '<li>❌ Le formulaire ne semble pas traiter les données</li>';
    print '</ul>';
    
    print '<h3>Points à vérifier :</h3>';
    print '<ul>';
    print '<li>Existence de la classe EmployerLoanPayment</li>';
    print '<li>Structure des tables de base de données</li>';
    print '<li>Code du formulaire de paiement</li>';
    print '<li>Permissions utilisateur</li>';
    print '<li>Gestion des erreurs</li>';
    print '</ul>';
    
    print '<p><a href="?action=debug" class="button" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;">Lancer le diagnostic</a></p>';
}

print '</body></html>';
