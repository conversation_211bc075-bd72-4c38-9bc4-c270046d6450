<?php
/**
 * Page de saisie de règlement pour prêt employé
 */

// Load Dolibarr environment
$res = 0;
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
if (!$res) $res = @include "../../../main.inc.php";
if (!$res) $res = @include "../../../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_payment.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';

// Load translation files
$langs->loadLangs(array("employerloan@employerloan", "compta", "bills", "loan"));

$id = GETPOST('id', 'int');
$action = GETPOST('action', 'alpha');
$cancel = GETPOST('cancel', 'alpha');

// Security check
if (!$user->hasRight('employerloan', 'write')) {
    accessforbidden();
}

$object = new EmployerLoan($db);
if ($id > 0) {
    $object->fetch($id);
}

/*
 * Actions
 */

if ($cancel) {
    header("Location: ".DOL_URL_ROOT."/custom/employerloan/card.php?id=".$id);
    exit;
}

// Create payment
if ($action == 'add_payment') {
    // Debug: Log des données reçues
    dol_syslog("DEBUG PAYMENT: Action = ".$action, LOG_DEBUG);
    dol_syslog("DEBUG PAYMENT: POST data = ".print_r($_POST, true), LOG_DEBUG);

    $datep = dol_mktime(12, 0, 0, GETPOST('datepmonth', 'int'), GETPOST('datepday', 'int'), GETPOST('datepyear', 'int'));
    $amount = price2num(GETPOST('amount', 'alpha'));
    $paymenttype = GETPOST('paymenttype', 'int');
    $accountid = GETPOST('accountid', 'int');
    $num_payment = GETPOST('num_payment', 'alpha');
    $note_private = GETPOST('note_private', 'restricthtml');
    $note_public = GETPOST('note_public', 'restricthtml');

    dol_syslog("DEBUG PAYMENT: amount=".$amount.", paymenttype=".$paymenttype.", accountid=".$accountid, LOG_DEBUG);

    if ($amount > 0 && $accountid > 0 && $paymenttype > 0) {
        dol_syslog("DEBUG PAYMENT: Validation OK, creating payment", LOG_DEBUG);

        $payment = new EmployerLoanPayment($db);
        $payment->fk_loan = $id;
        $payment->datep = $datep;
        $payment->amount_capital = $amount;
        $payment->amount_insurance = 0;
        $payment->amount_interest = 0;
        $payment->fk_typepayment = $paymenttype;
        $payment->num_payment = $num_payment;
        $payment->note_private = $note_private;
        $payment->note_public = $note_public;
        $payment->fk_bank = $accountid;

        dol_syslog("DEBUG PAYMENT: Calling create method", LOG_DEBUG);
        $result = $payment->create($user);
        dol_syslog("DEBUG PAYMENT: Create result = ".$result, LOG_DEBUG);

        if ($result > 0) {
            dol_syslog("DEBUG PAYMENT: Payment created successfully with ID ".$result, LOG_DEBUG);
            setEventMessages($langs->trans("PaymentRecorded"), null, 'mesgs');
            header("Location: ".DOL_URL_ROOT."/custom/employerloan/card.php?id=".$id);
            exit;
        } else {
            dol_syslog("DEBUG PAYMENT: Error creating payment: ".$payment->error, LOG_ERR);
            setEventMessages("Erreur lors de l'enregistrement du paiement: ".$payment->error, null, 'errors');
        }
    } else {
        dol_syslog("DEBUG PAYMENT: Validation failed - amount=".$amount.", paymenttype=".$paymenttype.", accountid=".$accountid, LOG_ERR);
        setEventMessages("Veuillez remplir tous les champs obligatoires (Montant: ".$amount.", Type: ".$paymenttype.", Compte: ".$accountid.")", null, 'errors');
    }
}

/*
 * View
 */

$form = new Form($db);
$formother = new FormOther($db);

llxHeader('', $langs->trans('DoPayment'));

// Object card
$head = array();
$head[0][0] = DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id;
$head[0][1] = $langs->trans("Card");
$head[0][2] = 'card';

$head[1][0] = DOL_URL_ROOT.'/custom/employerloan/payment/payment.php?id='.$object->id.'&action=create';
$head[1][1] = $langs->trans("Payment");
$head[1][2] = 'payment';

print dol_get_fiche_head($head, 'payment', $langs->trans("EmployerLoan"), -1, 'loan');

// Loan info
print '<table class="border centpercent tableforfield">';
print '<tr><td class="titlefield">'.$langs->trans("Ref").'</td><td>'.$object->ref.'</td></tr>';
print '<tr><td>'.$langs->trans("Label").'</td><td>'.$object->label.'</td></tr>';
print '<tr><td>'.$langs->trans("Capital").'</td><td>'.price($object->capital, 0, $langs, 1, -1, -1, $conf->currency).'</td></tr>';
print '<tr><td>'.$langs->trans("Employee").'</td><td>';
$employee = new User($db);
$employee->fetch($object->fk_employee);
print $employee->getFullName($langs);
print '</td></tr>';
print '</table>';

print dol_get_fiche_end();

print '<br>';

// Payment form
print '<form name="add_payment" action="'.$_SERVER['PHP_SELF'].'?id='.$id.'" method="POST">';
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="action" value="add_payment">';

print '<div class="fichecenter">';
print '<div class="underbanner clearboth"></div>';

print '<table class="border centpercent tableforfieldcreate">';

// Date
print '<tr><td class="titlefieldcreate fieldrequired">'.$langs->trans("Date").'</td>';
print '<td>';
print $form->selectDate(dol_now(), 'datep', 0, 0, 0, "add_payment", 1, 1);
print '</td></tr>';

// Payment mode
print '<tr><td class="fieldrequired">'.$langs->trans("PaymentMode").'</td>';
print '<td>';
print $form->select_types_paiements(52, 'paymenttype', '', 0, 1, 0, 0, 1, 'maxwidth200 widthcentpercentminusx', 1); // 52 = LCR par défaut
print '</td></tr>';

// Bank account
print '<tr><td class="fieldrequired">'.$langs->trans("BankAccount").'</td>';
print '<td>';
print $form->select_comptes(1, 'accountid', 0, '', 1, '', 0, 'maxwidth200 widthcentpercentminusx'); // 1 = premier compte par défaut
print '</td></tr>';

// Payment number (LCR)
print '<tr><td>'.$langs->trans("Numero").' (LCR)</td>';
print '<td><input type="text" name="num_payment" size="30" value=""></td></tr>';

// Private note
print '<tr><td>'.$langs->trans("NotePrivate").'</td>';
print '<td><textarea name="note_private" rows="3" cols="60"></textarea></td></tr>';

// Public note
print '<tr><td>'.$langs->trans("NotePublic").'</td>';
print '<td><textarea name="note_public" rows="3" cols="60"></textarea></td></tr>';

print '</table>';

print '<br>';

// Payment details table
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>'.$langs->trans("DueDate").'</th>';
print '<th>'.$langs->trans("LoanCapitalAccount").'</th>';
print '<th class="right">'.$langs->trans("AlreadyPaid").'</th>';
print '<th class="right">'.$langs->trans("RemainderToPay").'</th>';
print '<th class="right">'.$langs->trans("Amount").'</th>';
print '</tr>';

// Calculate amounts
$totalpaid = 0;
$sql_paid = "SELECT SUM(amount_capital + amount_insurance + amount_interest) as total_paid";
$sql_paid .= " FROM ".MAIN_DB_PREFIX."employer_payment_loan";
$sql_paid .= " WHERE fk_loan = ".(int) $object->id;
$resql_paid = $db->query($sql_paid);
if ($resql_paid) {
    $obj_paid = $db->fetch_object($resql_paid);
    $totalpaid = $obj_paid->total_paid ? $obj_paid->total_paid : 0;
}

$remainder = $object->capital - $totalpaid;

// Calculer le montant d'une échéance
$installment_amount = $object->capital / $object->nbterm;

print '<tr class="oddeven">';
print '<td>'.dol_print_date($object->dateend, 'day').'</td>';
print '<td>'.$object->accountancy_account_capital.'</td>';
print '<td class="right">'.price($totalpaid, 0, $langs, 1, -1, -1, $conf->currency).'</td>';
print '<td class="right">'.price($remainder, 0, $langs, 1, -1, -1, $conf->currency).'</td>';
print '<td class="right">';
print '<input type="text" name="amount" size="10" value="'.price($installment_amount).'" class="right">';
print '</td>';
print '</tr>';

print '</table>';

print '<div class="center">';
print '<input type="submit" class="button" value="'.$langs->trans("Save").'">';
print ' <input type="submit" class="button button-cancel" name="cancel" value="'.$langs->trans("Cancel").'">';
print '</div>';

print '</div>';
print '</form>';

llxFooter();