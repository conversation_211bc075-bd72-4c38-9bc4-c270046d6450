<?php
/* Copyright (C) 2024 NextGestion
 *
 * Content for committee setup tab in admin page
 */

if (!defined('NOREQUIRESOC')) {
    define('NOREQUIRESOC', '1');
}
if (!defined('NOREQUIRETRAN')) {
    define('NOREQUIRETRAN', '1');
}
if (!defined('NOCSRFCHECK')) {
    define('NOCSRFCHECK', '1');
}
if (!defined('NOTOKENRENEWAL')) {
    define('NOTOKENRENEWAL', '1');
}
if (!defined('NOREQUIREMENU')) {
    define('NOREQUIREMENU', '1');
}
if (!defined('NOREQUIREHTML')) {
    define('NOREQUIREHTML', '1');
}
if (!defined('NOREQUIREAJAX')) {
    define('NOREQUIREAJAX', '1');
}

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/user/class/user.class.php';
dol_include_once('/employerloan/class/employerloan_committee.class.php');

// Get parameters for committee
$action_committee = GETPOST('action_committee', 'aZ09');
$id_committee = GETPOST('id_committee', 'int');
$confirm_committee = GETPOST('confirm_committee', 'alpha');

// Initialize technical objects
$committee_object = new EmployerLoanCommittee($db);

/*
 * Actions for committee
 */

if ($action_committee == 'add' && GETPOST('cancel_committee', 'alpha') == '') {
    $error = 0;
    
    $committee_object->fk_user = GETPOST('fk_user', 'int');
    $committee_object->role = GETPOST('role', 'alpha');
    $committee_object->max_amount = GETPOST('max_amount', 'numeric');
    $committee_object->active = GETPOST('active', 'int');
    $committee_object->entity = $conf->entity;
    $committee_object->date_creation = dol_now();
    $committee_object->fk_user_creat = $user->id;
    
    if (empty($committee_object->fk_user)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("User")), null, 'errors');
        $error++;
    }
    if (empty($committee_object->role)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Role")), null, 'errors');
        $error++;
    }
    
    if (!$error) {
        $result = $committee_object->create($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberAdded"), null, 'mesgs');
            $action_committee = '';
        } else {
            setEventMessages($committee_object->error, $committee_object->errors, 'errors');
            $action_committee = 'create';
        }
    } else {
        $action_committee = 'create';
    }
}

if ($action_committee == 'update' && GETPOST('cancel_committee', 'alpha') == '') {
    $error = 0;
    
    if ($committee_object->fetch($id_committee) > 0) {
        $committee_object->fk_user = GETPOST('fk_user', 'int');
        $committee_object->role = GETPOST('role', 'alpha');
        $committee_object->max_amount = GETPOST('max_amount', 'numeric');
        $committee_object->active = GETPOST('active', 'int');
        
        if (empty($committee_object->fk_user)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("User")), null, 'errors');
            $error++;
        }
        if (empty($committee_object->role)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Role")), null, 'errors');
            $error++;
        }
        
        if (!$error) {
            $result = $committee_object->update($user);
            if ($result > 0) {
                setEventMessages($langs->trans("CommitteeMemberUpdated"), null, 'mesgs');
                $action_committee = '';
            } else {
                setEventMessages($committee_object->error, $committee_object->errors, 'errors');
                $action_committee = 'edit';
            }
        } else {
            $action_committee = 'edit';
        }
    }
}

if ($action_committee == 'confirm_delete' && $confirm_committee == 'yes') {
    if ($committee_object->fetch($id_committee) > 0) {
        $result = $committee_object->delete($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberDeleted"), null, 'mesgs');
            $action_committee = '';
        } else {
            setEventMessages($committee_object->error, $committee_object->errors, 'errors');
        }
    }
}

if ($action_committee == 'activate') {
    if ($committee_object->fetch($id_committee) > 0) {
        $committee_object->active = 1;
        $result = $committee_object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberActivated"), null, 'mesgs');
        }
    }
}

if ($action_committee == 'disable') {
    if ($committee_object->fetch($id_committee) > 0) {
        $committee_object->active = 0;
        $result = $committee_object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberDisabled"), null, 'mesgs');
        }
    }
}

/*
 * View for committee
 */

// Buttons
if ($action_committee != 'create' && $action_committee != 'edit') {
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?tab=committee&action_committee=create">'.$langs->trans('AddCommitteeMember').'</a>';
    print '</div>';
}

// Form to create/edit committee member
if ($action_committee == 'create' || $action_committee == 'edit') {
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'?tab=committee">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action_committee" value="'.($action_committee == 'create' ? 'add' : 'update').'">';
    if ($action_committee == 'edit') {
        print '<input type="hidden" name="id_committee" value="'.$id_committee.'">';
        $committee_object->fetch($id_committee);
    }
    
    print '<table class="border centpercent">';
    
    // User
    print '<tr><td class="fieldrequired">'.$langs->trans('User').'</td>';
    print '<td>'.$form->select_dolusers(($action_committee == 'edit' ? $committee_object->fk_user : GETPOST('fk_user', 'int')), 'fk_user', 1, '', 0, '', '', $conf->entity, 0, 0, '', 0, '', 'maxwidth300').'</td></tr>';
    
    // Role
    print '<tr><td class="fieldrequired">'.$langs->trans('Role').'</td>';
    $roles = array(
        'president' => $langs->trans('President'),
        'member' => $langs->trans('Member'),
        'secretary' => $langs->trans('Secretary'),
        'observer' => $langs->trans('Observer')
    );
    print '<td>'.$form->selectarray('role', $roles, ($action_committee == 'edit' ? $committee_object->role : GETPOST('role', 'alpha')), 1).'</td></tr>';
    
    // Max amount
    print '<tr><td>'.$langs->trans('MaxAmount').'</td>';
    print '<td><input type="number" name="max_amount" step="0.01" min="0" value="'.($action_committee == 'edit' ? $committee_object->max_amount : GETPOST('max_amount', 'numeric')).'" placeholder="'.$langs->trans('NoLimit').'"></td></tr>';
    
    // Active
    print '<tr><td>'.$langs->trans('Active').'</td>';
    print '<td>'.$form->selectyesno('active', ($action_committee == 'edit' ? $committee_object->active : 1), 1).'</td></tr>';
    
    print '</table>';
    
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.($action_committee == 'create' ? $langs->trans('Add') : $langs->trans('Modify')).'">';
    print ' &nbsp; ';
    print '<input type="submit" class="button button-cancel" name="cancel_committee" value="'.$langs->trans('Cancel').'">';
    print '</div>';
    
    print '</form>';
}

// List of committee members
if ($action_committee != 'create' && $action_committee != 'edit') {
    $sql = "SELECT c.rowid, c.fk_user, c.role, c.max_amount, c.active, c.date_creation,";
    $sql .= " u.lastname, u.firstname, u.login";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_committee as c";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user as u ON c.fk_user = u.rowid";
    $sql .= " WHERE c.entity = ".$conf->entity;
    $sql .= " ORDER BY c.role ASC, u.lastname ASC";
    
    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);
        
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th>'.$langs->trans('User').'</th>';
        print '<th>'.$langs->trans('Role').'</th>';
        print '<th>'.$langs->trans('MaxAmount').'</th>';
        print '<th>'.$langs->trans('Status').'</th>';
        print '<th>'.$langs->trans('DateCreation').'</th>';
        print '<th width="60">'.$langs->trans('Action').'</th>';
        print '</tr>';
        
        if ($num > 0) {
            $i = 0;
            while ($i < $num) {
                $obj = $db->fetch_object($resql);
                
                print '<tr class="oddeven">';
                print '<td>';
                $userstatic = new User($db);
                $userstatic->id = $obj->fk_user;
                $userstatic->lastname = $obj->lastname;
                $userstatic->firstname = $obj->firstname;
                $userstatic->login = $obj->login;
                print $userstatic->getNomUrl(1);
                print '</td>';
                print '<td>';
                $roles = array(
                    'president' => $langs->trans('President'),
                    'member' => $langs->trans('Member'),
                    'secretary' => $langs->trans('Secretary'),
                    'observer' => $langs->trans('Observer')
                );
                print (isset($roles[$obj->role]) ? $roles[$obj->role] : $obj->role);
                print '</td>';
                print '<td class="amount">'.($obj->max_amount ? price($obj->max_amount) : $langs->trans('NoLimit')).'</td>';
                print '<td class="center">';
                if ($obj->active) {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?tab=committee&action_committee=disable&id_committee='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Enabled"), 'switch_on');
                    print '</a>';
                } else {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?tab=committee&action_committee=activate&id_committee='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Disabled"), 'switch_off');
                    print '</a>';
                }
                print '</td>';
                print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'day').'</td>';
                print '<td class="center">';
                print '<a href="'.$_SERVER["PHP_SELF"].'?tab=committee&action_committee=edit&id_committee='.$obj->rowid.'">';
                print img_edit();
                print '</a>';
                print ' ';
                print '<a href="'.$_SERVER["PHP_SELF"].'?tab=committee&action_committee=delete&id_committee='.$obj->rowid.'&token='.newToken().'" onclick="return confirm(\''.$langs->trans('ConfirmDeleteCommitteeMember').'\');">';
                print img_delete();
                print '</a>';
                print '</td>';
                print '</tr>';
                
                $i++;
            }
        } else {
            print '<tr><td colspan="6"><span class="opacitymedium">'.$langs->trans("NoCommitteeMemberFound").'</span></td></tr>';
        }
        
        print '</table>';
        
        $db->free($resql);
    } else {
        dol_print_error($db);
    }
}
