<?php
/**
 * Page de paiement d'échéances de prêt employé
 */

// Load Dolibarr environment
$res = 0;
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
if (!$res) $res = @include "../../main.inc.php";
if (!$res) $res = @include "../../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_payment.class.php';
require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';

// Load translation files
$langs->loadLangs(array("employerloan@employerloan", "compta", "bills", "loan"));

$id = GETPOST('id', 'int');
$installment_id = GETPOST('installment_id', 'int');
$action = GETPOST('action', 'alpha');
$confirm = GETPOST('confirm', 'alpha');

// Security check
if (!$user->hasRight('loan', 'write')) {
    accessforbidden();
}

$object = new EmployerLoan($db);
if ($id > 0) {
    $object->fetch($id);
}

/*
 * Actions
 */

// Pay installment
if ($action == 'pay_installment' && $installment_id > 0) {
    $amount = price2num(GETPOST('amount', 'alpha'));
    $bank_account = GETPOST('bank_account', 'int');
    
    if ($amount > 0 && $bank_account > 0) {
        $payment = new EmployerLoanPayment($db);
        $result = $payment->createInstallmentPayment($id, $installment_id, $amount, $bank_account, $user);
        
        if ($result > 0) {
            setEventMessages($langs->trans("PaymentRecorded"), null, 'mesgs');
            header("Location: ".$_SERVER['PHP_SELF']."?id=".$id);
            exit;
        } else {
            setEventMessages($payment->error, null, 'errors');
        }
    } else {
        setEventMessages($langs->trans("ErrorFieldRequired"), null, 'errors');
    }
}

/*
 * View
 */

$form = new Form($db);

llxHeader('', $langs->trans('LoanInstallmentPayment'));

// Object card
$head = array();
$head[0][0] = DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id;
$head[0][1] = $langs->trans("Card");
$head[0][2] = 'card';

$head[1][0] = DOL_URL_ROOT.'/custom/employerloan/payment_installment.php?id='.$object->id;
$head[1][1] = $langs->trans("InstallmentPayments");
$head[1][2] = 'payments';

print dol_get_fiche_head($head, 'payments', $langs->trans("EmployerLoan"), -1, 'loan');

// Loan info
print '<table class="border centpercent tableforfield">';
print '<tr><td class="titlefield">'.$langs->trans("Ref").'</td><td>'.$object->ref.'</td></tr>';
print '<tr><td>'.$langs->trans("Label").'</td><td>'.$object->label.'</td></tr>';
print '<tr><td>'.$langs->trans("Capital").'</td><td>'.price($object->capital).'</td></tr>';
print '<tr><td>'.$langs->trans("Employee").'</td><td>';
$employee = new User($db);
$employee->fetch($object->fk_employee);
print $employee->getFullName($langs);
print '</td></tr>';
print '</table>';

print dol_get_fiche_end();

// Installments list
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>'.$langs->trans("InstallmentNumber").'</th>';
print '<th>'.$langs->trans("DueDate").'</th>';
print '<th class="right">'.$langs->trans("AmountPrincipal").'</th>';
print '<th class="right">'.$langs->trans("AmountTotal").'</th>';
print '<th>'.$langs->trans("Status").'</th>';
print '<th class="center">'.$langs->trans("Action").'</th>';
print '</tr>';

// Get installments
$sql = "SELECT rowid, installment_number, due_date, amount_principal, amount_total, status, date_payment, amount_paid";
$sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_installment";
$sql .= " WHERE fk_loan = ".(int) $object->id;
$sql .= " ORDER BY installment_number";

$resql = $db->query($sql);
if ($resql) {
    $num = $db->num_rows($resql);
    $i = 0;
    
    while ($i < $num) {
        $obj = $db->fetch_object($resql);
        
        print '<tr class="oddeven">';
        print '<td>'.$obj->installment_number.'</td>';
        print '<td>'.dol_print_date($db->jdate($obj->due_date), 'day').'</td>';
        print '<td class="right">'.price($obj->amount_principal).'</td>';
        print '<td class="right">'.price($obj->amount_total).'</td>';
        
        // Status
        print '<td>';
        if ($obj->status == 'paid') {
            print '<span class="badge badge-status4 badge-status">'.$langs->trans("Paid").'</span>';
            if ($obj->date_payment) {
                print '<br><small>'.dol_print_date($db->jdate($obj->date_payment), 'day').'</small>';
            }
        } else {
            print '<span class="badge badge-status1 badge-status">'.$langs->trans("Pending").'</span>';
        }
        print '</td>';
        
        // Action
        print '<td class="center">';
        if ($obj->status != 'paid') {
            print '<a href="#" onclick="showPaymentForm('.$obj->rowid.', '.$obj->amount_total.')" class="button">'.$langs->trans("Pay").'</a>';
        } else {
            print '<span class="opacitymedium">'.$langs->trans("Paid").'</span>';
        }
        print '</td>';
        
        print '</tr>';
        $i++;
    }
    
    $db->free($resql);
} else {
    print '<tr><td colspan="6">'.$langs->trans("NoInstallments").'</td></tr>';
}

print '</table>';
print '</div>';

// Payment form (hidden by default)
print '<div id="payment-form" style="display: none; margin-top: 20px;">';
print '<form method="POST" action="'.$_SERVER['PHP_SELF'].'?id='.$object->id.'">';
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="action" value="pay_installment">';
print '<input type="hidden" name="installment_id" id="installment_id" value="">';

print '<div class="div-table-responsive-no-min">';
print '<table class="border centpercent">';
print '<tr><td class="titlefield fieldrequired">'.$langs->trans("Amount").'</td>';
print '<td><input type="text" name="amount" id="payment_amount" size="10" required></td></tr>';

print '<tr><td class="fieldrequired">'.$langs->trans("BankAccount").'</td>';
print '<td>';
print $form->select_comptes('', 'bank_account', 0, '', 1);
print '</td></tr>';

print '</table>';
print '</div>';

print '<div class="center">';
print '<input type="submit" class="button" value="'.$langs->trans("RecordPayment").'">';
print ' <input type="button" class="button button-cancel" value="'.$langs->trans("Cancel").'" onclick="hidePaymentForm()">';
print '</div>';

print '</form>';
print '</div>';

// JavaScript
print '<script type="text/javascript">
function showPaymentForm(installmentId, amount) {
    document.getElementById("installment_id").value = installmentId;
    document.getElementById("payment_amount").value = amount;
    document.getElementById("payment-form").style.display = "block";
}

function hidePaymentForm() {
    document.getElementById("payment-form").style.display = "none";
}
</script>';

llxFooter();
