<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

/**
 * \file    class/employerloan_committee.class.php
 * \ingroup employerloan
 * \brief   Classe pour le comité d'évaluation des prêts
 */

require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

/**
 * Classe pour le comité d'évaluation des demandes de crédit
 */
class EmployerLoanCommittee extends CommonObject
{
    /**
     * @var string ID of module.
     */
    public $module = 'employerloan';

    /**
     * @var string ID to identify managed object.
     */
    public $element = 'employerloan_committee';

    /**
     * @var string Name of table without prefix where object is stored.
     */
    public $table_element = 'employer_loan_committee';

    /**
     * @var int  Does this object support multicompany module ?
     */
    public $ismultientitymanaged = 1;

    /**
     * @var int  Does object support extrafields ? 0=No, 1=Yes
     */
    public $isextrafieldmanaged = 0;

    /**
     * @var string String with name of icon for employerloan_committee.
     */
    public $picto = 'employerloan_committee@employerloan';

    // Object fields
    public $rowid;
    public $entity;
    public $fk_user;
    public $role;
    public $max_amount;
    public $active;
    public $date_creation;
    public $fk_user_creat;

    /**
     * @var DoliDB Database handler
     */
    public $db;

    /**
     * Constructor
     *
     * @param DoliDb $db Database handler
     */
    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     * Create object into database
     *
     * @param  User $user      User that creates
     * @param  bool $notrigger false=launch triggers after, true=disable triggers
     * @return int             <0 if KO, Id of created object if OK
     */
    public function create(User $user, $notrigger = false)
    {
        global $conf;

        $this->db->begin();

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_committee";
        $sql .= " (entity, fk_user, role, max_amount, active, date_creation, fk_user_creat)";
        $sql .= " VALUES (".$conf->entity.", ".$this->fk_user.", '".$this->db->escape($this->role)."', ";
        $sql .= ($this->max_amount ? $this->max_amount : "NULL").", ".$this->active.", '".$this->db->idate(dol_now())."', ".$user->id.")";

        $resql = $this->db->query($sql);
        if ($resql) {
            $this->id = $this->db->last_insert_id(MAIN_DB_PREFIX."employer_loan_committee");
            $this->db->commit();
            return $this->id;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Load object in memory from the database
     *
     * @param int    $id   Id object
     * @return int         <0 if KO, 0 if not found, >0 if OK
     */
    public function fetch($id)
    {
        $sql = "SELECT rowid, entity, fk_user, role, max_amount, active, date_creation, fk_user_creat";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_committee";
        $sql .= " WHERE rowid = ".$id;

        $resql = $this->db->query($sql);
        if ($resql) {
            if ($this->db->num_rows($resql)) {
                $obj = $this->db->fetch_object($resql);

                $this->id = $obj->rowid;
                $this->rowid = $obj->rowid;
                $this->entity = $obj->entity;
                $this->fk_user = $obj->fk_user;
                $this->role = $obj->role;
                $this->max_amount = $obj->max_amount;
                $this->active = $obj->active;
                $this->date_creation = $this->db->jdate($obj->date_creation);
                $this->fk_user_creat = $obj->fk_user_creat;

                $this->db->free($resql);
                return 1;
            } else {
                $this->db->free($resql);
                return 0;
            }
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     * Update object into database
     *
     * @param  User $user      User that modifies
     * @param  bool $notrigger false=launch triggers after, true=disable triggers
     * @return int             <0 if KO, >0 if OK
     */
    public function update(User $user, $notrigger = false)
    {
        $this->db->begin();

        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan_committee SET";
        $sql .= " fk_user = ".$this->fk_user;
        $sql .= ", role = '".$this->db->escape($this->role)."'";
        $sql .= ", max_amount = ".($this->max_amount ? $this->max_amount : "NULL");
        $sql .= ", active = ".$this->active;
        $sql .= " WHERE rowid = ".$this->id;

        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Delete object in database
     *
     * @param User $user       User that deletes
     * @param bool $notrigger  false=launch triggers after, true=disable triggers
     * @return int             <0 if KO, >0 if OK
     */
    public function delete(User $user, $notrigger = false)
    {
        $this->db->begin();

        $sql = "DELETE FROM ".MAIN_DB_PREFIX."employer_loan_committee";
        $sql .= " WHERE rowid = ".$this->id;

        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Évaluer une demande de crédit selon les critères définis
     *
     * @param EmployerLoanRequest $request Demande à évaluer
     * @param User                $evaluator Évaluateur
     * @return array                       Résultat de l'évaluation
     */
    public function evaluateRequest($request, $evaluator)
    {
        global $conf;

        $this->db->begin();

        try {
            // Récupérer les critères actifs
            $criteria = $this->getCriteria();
            
            $totalScore = 0;
            $maxScore = 0;
            $evaluationDetails = array();

            foreach ($criteria as $criterion) {
                $score = $this->evaluateCriterion($request, $criterion);
                $weightedScore = $score * $criterion['weight'];
                
                $totalScore += $weightedScore;
                $maxScore += 100 * $criterion['weight']; // Score max = 100 par critère
                
                $evaluationDetails[] = array(
                    'criterion' => $criterion,
                    'score' => $score,
                    'weighted_score' => $weightedScore
                );
                
                // Enregistrer l'évaluation détaillée
                $this->saveEvaluationDetail($request->id, $criterion['rowid'], $score, $evaluator);
            }

            // Calculer le score final (pourcentage)
            $finalScore = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;

            // Mettre à jour la demande avec le score
            $this->updateRequestEvaluation($request, $finalScore, $evaluator);

            // Déterminer la recommandation
            $recommendation = $this->getRecommendation($finalScore, $request);

            $this->db->commit();

            return array(
                'success' => true,
                'final_score' => $finalScore,
                'recommendation' => $recommendation,
                'details' => $evaluationDetails
            );

        } catch (Exception $e) {
            $this->db->rollback();
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Récupérer les critères d'évaluation actifs
     *
     * @return array Critères
     */
    public function getCriteria()
    {
        global $conf;

        $criteria = array();

        $sql = "SELECT rowid, code, label, description, weight, min_value, max_value, formula";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_criteria";
        $sql .= " WHERE entity = ".$conf->entity;
        $sql .= " AND active = 1";
        $sql .= " ORDER BY weight DESC, label";

        $resql = $this->db->query($sql);
        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $criteria[] = array(
                    'rowid' => $obj->rowid,
                    'code' => $obj->code,
                    'label' => $obj->label,
                    'description' => $obj->description,
                    'weight' => $obj->weight,
                    'min_value' => $obj->min_value,
                    'max_value' => $obj->max_value,
                    'formula' => $obj->formula
                );
            }
        }

        return $criteria;
    }

    /**
     * Évaluer un critère spécifique
     *
     * @param EmployerLoanRequest $request Demande
     * @param array               $criterion Critère
     * @return float                       Score (0-100)
     */
    private function evaluateCriterion($request, $criterion)
    {
        switch ($criterion['code']) {
            case 'SALARY_RATIO':
                return $this->evaluateSalaryRatio($request, $criterion);
                
            case 'SENIORITY':
                return $this->evaluateSeniority($request, $criterion);
                
            case 'EXISTING_LOANS':
                return $this->evaluateExistingLoans($request, $criterion);
                
            case 'PAYMENT_HISTORY':
                return $this->evaluatePaymentHistory($request, $criterion);
                
            case 'GUARANTOR':
                return $this->evaluateGuarantor($request, $criterion);
                
            case 'PURPOSE':
                return $this->evaluatePurpose($request, $criterion);
                
            default:
                return 50; // Score neutre par défaut
        }
    }

    /**
     * Évaluer le ratio salaire/prêt
     */
    private function evaluateSalaryRatio($request, $criterion)
    {
        if (empty($request->monthly_salary) || $request->monthly_salary <= 0) {
            return 0;
        }

        $ratio = $request->amount_requested / $request->monthly_salary;
        $maxRatio = $criterion['max_value'] ?: 10;

        if ($ratio <= $maxRatio * 0.5) {
            return 100; // Excellent
        } elseif ($ratio <= $maxRatio * 0.7) {
            return 80; // Bon
        } elseif ($ratio <= $maxRatio) {
            return 60; // Acceptable
        } else {
            return 20; // Risqué
        }
    }

    /**
     * Évaluer l'ancienneté
     */
    private function evaluateSeniority($request, $criterion)
    {
        // Récupérer la date d'embauche de l'employé
        $sql = "SELECT datec FROM ".MAIN_DB_PREFIX."user WHERE rowid = ".(int)$request->fk_employee;
        $resql = $this->db->query($sql);
        
        if ($resql && $this->db->num_rows($resql)) {
            $obj = $this->db->fetch_object($resql);
            $hireDate = $this->db->jdate($obj->datec);
            $monthsWorked = (time() - $hireDate) / (30 * 24 * 3600); // Approximation en mois
            
            $minMonths = $criterion['min_value'] ?: 6;
            
            if ($monthsWorked >= $minMonths * 2) {
                return 100; // Excellent
            } elseif ($monthsWorked >= $minMonths * 1.5) {
                return 80; // Bon
            } elseif ($monthsWorked >= $minMonths) {
                return 60; // Acceptable
            } else {
                return 20; // Insuffisant
            }
        }
        
        return 0;
    }

    /**
     * Évaluer les prêts existants
     */
    private function evaluateExistingLoans($request, $criterion)
    {
        $maxAmount = $criterion['max_value'] ?: 50000;
        $existingLoans = $request->other_loans ?: 0;

        if ($existingLoans == 0) {
            return 100; // Aucun prêt existant
        } elseif ($existingLoans <= $maxAmount * 0.3) {
            return 80; // Faible endettement
        } elseif ($existingLoans <= $maxAmount * 0.6) {
            return 60; // Endettement modéré
        } elseif ($existingLoans <= $maxAmount) {
            return 40; // Endettement élevé
        } else {
            return 10; // Surendettement
        }
    }

    /**
     * Évaluer l'historique des paiements
     */
    private function evaluatePaymentHistory($request, $criterion)
    {
        // Vérifier l'historique des paiements précédents
        $sql = "SELECT COUNT(*) as total_loans, ";
        $sql .= "SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_loans";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan";
        $sql .= " WHERE fk_employee = ".(int)$request->fk_employee;
        $sql .= " AND rowid != ".(int)$request->id;

        $resql = $this->db->query($sql);
        if ($resql && $this->db->num_rows($resql)) {
            $obj = $this->db->fetch_object($resql);
            
            if ($obj->total_loans == 0) {
                return 70; // Nouveau client, score neutre
            }
            
            $successRate = ($obj->completed_loans / $obj->total_loans) * 100;
            
            if ($successRate >= 90) {
                return 100; // Excellent historique
            } elseif ($successRate >= 70) {
                return 80; // Bon historique
            } elseif ($successRate >= 50) {
                return 60; // Historique moyen
            } else {
                return 30; // Mauvais historique
            }
        }
        
        return 70; // Score neutre par défaut
    }

    /**
     * Évaluer la présence d'un garant
     */
    private function evaluateGuarantor($request, $criterion)
    {
        if (!empty($request->guarantor_name) && !empty($request->guarantor_phone)) {
            return 100; // Garant présent
        } else {
            return 30; // Pas de garant
        }
    }

    /**
     * Évaluer l'objet du prêt
     */
    private function evaluatePurpose($request, $criterion)
    {
        $purpose = strtolower($request->purpose);
        
        // Objets prioritaires
        $highPriority = array('santé', 'éducation', 'formation', 'urgence', 'famille');
        $mediumPriority = array('logement', 'transport', 'équipement');
        $lowPriority = array('voyage', 'loisir', 'luxe');
        
        foreach ($highPriority as $keyword) {
            if (strpos($purpose, $keyword) !== false) {
                return 100;
            }
        }
        
        foreach ($mediumPriority as $keyword) {
            if (strpos($purpose, $keyword) !== false) {
                return 70;
            }
        }
        
        foreach ($lowPriority as $keyword) {
            if (strpos($purpose, $keyword) !== false) {
                return 40;
            }
        }
        
        return 60; // Score neutre
    }

    /**
     * Enregistrer le détail d'évaluation d'un critère
     */
    private function saveEvaluationDetail($requestId, $criteriaId, $score, $evaluator)
    {
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_evaluation (";
        $sql .= "fk_loan_request, fk_criteria, value_obtained, score, date_evaluation, fk_user_evaluator";
        $sql .= ") VALUES (";
        $sql .= (int)$requestId.",";
        $sql .= (int)$criteriaId.",";
        $sql .= (float)$score.",";
        $sql .= (float)$score.",";
        $sql .= "NOW(),";
        $sql .= (int)$evaluator->id;
        $sql .= ")";

        return $this->db->query($sql);
    }

    /**
     * Mettre à jour la demande avec le résultat de l'évaluation
     */
    private function updateRequestEvaluation($request, $score, $evaluator)
    {
        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan_request SET";
        $sql .= " evaluation_score = ".(float)$score.",";
        $sql .= " fk_user_evaluator = ".(int)$evaluator->id.",";
        $sql .= " date_evaluation = NOW(),";
        $sql .= " status = ".EmployerLoanRequest::STATUS_EVALUATING;
        $sql .= " WHERE rowid = ".(int)$request->id;

        return $this->db->query($sql);
    }

    /**
     * Obtenir une recommandation basée sur le score
     */
    private function getRecommendation($score, $request)
    {
        if ($score >= 80) {
            return array(
                'decision' => 'approve',
                'message' => 'Demande recommandée pour approbation - Score excellent',
                'conditions' => array()
            );
        } elseif ($score >= 65) {
            return array(
                'decision' => 'approve_conditional',
                'message' => 'Demande recommandée avec conditions - Score bon',
                'conditions' => array(
                    'Réduire le montant de 10%',
                    'Garant obligatoire si pas déjà fourni'
                )
            );
        } elseif ($score >= 50) {
            return array(
                'decision' => 'review',
                'message' => 'Demande nécessite un examen approfondi - Score moyen',
                'conditions' => array(
                    'Entretien avec le demandeur',
                    'Vérification des garanties',
                    'Réduction possible du montant'
                )
            );
        } else {
            return array(
                'decision' => 'reject',
                'message' => 'Demande recommandée pour rejet - Score insuffisant',
                'conditions' => array(
                    'Critères d\'éligibilité non remplis',
                    'Risque trop élevé'
                )
            );
        }
    }

    /**
     * Obtenir les membres du comité
     *
     * @return array Membres du comité
     */
    public function getCommitteeMembers()
    {
        global $conf;

        $members = array();

        $sql = "SELECT c.rowid, c.fk_user, c.role, c.max_amount, c.active,";
        $sql .= " u.firstname, u.lastname, u.email";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_committee c";
        $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user u ON c.fk_user = u.rowid";
        $sql .= " WHERE c.entity = ".$conf->entity;
        $sql .= " AND c.active = 1";
        $sql .= " ORDER BY c.role, u.lastname, u.firstname";

        $resql = $this->db->query($sql);
        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $members[] = array(
                    'rowid' => $obj->rowid,
                    'fk_user' => $obj->fk_user,
                    'role' => $obj->role,
                    'max_amount' => $obj->max_amount,
                    'firstname' => $obj->firstname,
                    'lastname' => $obj->lastname,
                    'email' => $obj->email
                );
            }
        }

        return $members;
    }

    /**
     * Enregistrer une décision du comité
     *
     * @param int    $requestId ID de la demande
     * @param int    $memberId  ID du membre du comité
     * @param string $decision  Décision (approve, reject, abstain)
     * @param string $comments  Commentaires
     * @return bool             Succès
     */
    public function recordCommitteeDecision($requestId, $memberId, $decision, $comments = '')
    {
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_committee_decision (";
        $sql .= "fk_loan_request, fk_committee_member, decision, comments, date_decision";
        $sql .= ") VALUES (";
        $sql .= (int)$requestId.",";
        $sql .= (int)$memberId.",";
        $sql .= "'".$this->db->escape($decision)."',";
        $sql .= "'".$this->db->escape($comments)."',";
        $sql .= "NOW()";
        $sql .= ")";

        return $this->db->query($sql);
    }

    /**
     * Vérifier si une demande peut être approuvée
     *
     * @param int $requestId ID de la demande
     * @return array         Résultat de la vérification
     */
    public function checkApprovalStatus($requestId)
    {
        // Compter les décisions
        $sql = "SELECT decision, COUNT(*) as count";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_committee_decision";
        $sql .= " WHERE fk_loan_request = ".(int)$requestId;
        $sql .= " GROUP BY decision";

        $decisions = array('approve' => 0, 'reject' => 0, 'abstain' => 0);
        
        $resql = $this->db->query($sql);
        if ($resql) {
            while ($obj = $this->db->fetch_object($resql)) {
                $decisions[$obj->decision] = $obj->count;
            }
        }

        $totalVotes = $decisions['approve'] + $decisions['reject'];
        $approvalRate = $totalVotes > 0 ? ($decisions['approve'] / $totalVotes) * 100 : 0;

        // Règle : 60% d'approbation minimum
        $approved = $approvalRate >= 60;

        return array(
            'approved' => $approved,
            'approval_rate' => $approvalRate,
            'votes' => $decisions,
            'total_votes' => $totalVotes
        );
    }

    /**
     * Return clicable name (with picto eventually)
     *
     * @param int $withpicto 0=No picto, 1=Include picto into link, 2=Only picto
     * @param string $option Where point the link ('card', 'nolink', ...)
     * @param int $notooltip 1=Disable tooltip
     * @param string $morecss Add more css on link
     * @param int $save_lastsearch_value -1=Auto, 0=No save of lastsearch_values when clicking, 1=Save lastsearch_values whenclicking
     * @return string HTML
     */
    public function getNomUrl($withpicto = 0, $option = '', $notooltip = 0, $morecss = '', $save_lastsearch_value = -1)
    {
        global $conf, $langs;

        if (!empty($conf->dol_no_mouse_hover)) {
            $notooltip = 1; // Force disable tooltips
        }

        $result = '';

        $label = img_picto('', 'group').' <u>'.$langs->trans("LoanCommittee").'</u>';
        $label .= '<br>';
        $label .= '<b>'.$langs->trans('Ref').':</b> '.$this->rowid.'<br>';
        $label .= '<b>'.$langs->trans('Name').':</b> '.$this->name.'<br>';

        $url = DOL_URL_ROOT.'/custom/employerloan/committee_card.php?id='.$this->rowid;

        if ($option != 'nolink') {
            // Add param to save lastsearch_values or not
            $add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
            if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
                $add_save_lastsearch_values = 1;
            }
            if ($add_save_lastsearch_values) {
                $url .= '&save_lastsearch_values=1';
            }
        }

        $linkclose = '';
        if (empty($notooltip)) {
            if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
                $label = $langs->trans("ShowCommittee");
                $linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
            }
            $linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
            $linkclose .= ' class="classfortooltip'.($morecss ? ' '.$morecss : '').'"';
        } else {
            $linkclose = ($morecss ? ' class="'.$morecss.'"' : '');
        }

        $linkstart = '<a href="'.$url.'"';
        $linkstart .= $linkclose.'>';
        $linkend = '</a>';

        $result .= $linkstart;
        if ($withpicto) {
            $result .= img_object(($notooltip ? '' : $label), 'group', ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
        }
        if ($withpicto != 2) {
            $result .= $this->name;
        }
        $result .= $linkend;

        return $result;
    }

    /**
     * Return the status
     *
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function getLibStatut($mode = 0)
    {
        global $langs;

        $statusType = 'status4';
        $labelStatus = $langs->trans('Active');
        $labelStatusShort = $langs->trans('Active');

        if ($this->active == 0) {
            $statusType = 'status5';
            $labelStatus = $langs->trans('Inactive');
            $labelStatusShort = $langs->trans('Inactive');
        }

        return dolGetStatus($labelStatus, $labelStatusShort, '', $statusType, $mode);
    }

    /**
     * Return the status
     *
     * @param int $status Id status
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        return $this->getLibStatut($mode);
    }
}
