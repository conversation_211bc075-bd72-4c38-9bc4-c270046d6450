<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

/**
 * \file    class/employerloan_request.class.php
 * \ingroup employerloan
 * \brief   Classe pour les demandes de crédit salarié
 */

require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

/**
 * Classe pour les demandes de crédit salarié
 */
class EmployerLoanRequest extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'employerloan_request';

    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'employer_loan_request';

    /**
     * @var string String with name of icon for myobject. Must be the part after the 'object_' into object_myobject.png
     */
    public $picto = 'money-bill-alt';

    /**
     * @var array  Array with all fields and their property. Do not use it as a static var. It may be modified by constructor.
     */
    public $fields = array(
        'rowid' => array('type'=>'integer', 'label'=>'TechnicalID', 'enabled'=>'1', 'position'=>1, 'notnull'=>1, 'visible'=>0, 'noteditable'=>'1', 'index'=>1, 'comment'=>"Id"),
        'entity' => array('type'=>'integer', 'label'=>'Entity', 'enabled'=>'1', 'position'=>2, 'notnull'=>1, 'visible'=>0, 'default'=>'1'),
        'ref' => array('type'=>'varchar(30)', 'label'=>'Ref', 'enabled'=>'1', 'position'=>10, 'notnull'=>1, 'visible'=>1, 'index'=>1, 'searchall'=>1, 'showoncombobox'=>'1', 'comment'=>"Reference of object"),
        'fk_employee' => array('type'=>'integer:User:user/class/user.class.php', 'label'=>'Employee', 'enabled'=>'1', 'position'=>20, 'notnull'=>1, 'visible'=>1),
        'amount_requested' => array('type'=>'price', 'label'=>'AmountRequested', 'enabled'=>'1', 'position'=>30, 'notnull'=>1, 'visible'=>1),
        'purpose' => array('type'=>'text', 'label'=>'Purpose', 'enabled'=>'1', 'position'=>40, 'notnull'=>1, 'visible'=>1),
        'duration_months' => array('type'=>'integer', 'label'=>'DurationMonths', 'enabled'=>'1', 'position'=>50, 'notnull'=>1, 'visible'=>1),
        'monthly_salary' => array('type'=>'price', 'label'=>'MonthlySalary', 'enabled'=>'1', 'position'=>55, 'notnull'=>0, 'visible'=>1),
        'other_loans' => array('type'=>'price', 'label'=>'OtherLoans', 'enabled'=>'1', 'position'=>56, 'notnull'=>0, 'visible'=>1, 'default'=>'0'),
        'guarantor_name' => array('type'=>'varchar(100)', 'label'=>'GuarantorName', 'enabled'=>'1', 'position'=>57, 'notnull'=>0, 'visible'=>1),
        'guarantor_phone' => array('type'=>'varchar(20)', 'label'=>'GuarantorPhone', 'enabled'=>'1', 'position'=>58, 'notnull'=>0, 'visible'=>1),
        'guarantor_address' => array('type'=>'text', 'label'=>'GuarantorAddress', 'enabled'=>'1', 'position'=>59, 'notnull'=>0, 'visible'=>1),
        'status' => array('type'=>'integer', 'label'=>'Status', 'enabled'=>'1', 'position'=>60, 'notnull'=>1, 'visible'=>1),
        'date_request' => array('type'=>'datetime', 'label'=>'DateRequest', 'enabled'=>'1', 'position'=>70, 'notnull'=>1, 'visible'=>1),
        'date_decision' => array('type'=>'datetime', 'label'=>'DateDecision', 'enabled'=>'1', 'position'=>75, 'notnull'=>0, 'visible'=>1),
        'rejection_reason' => array('type'=>'text', 'label'=>'RejectionReason', 'enabled'=>'1', 'position'=>80, 'notnull'=>0, 'visible'=>1),
        'evaluation_score' => array('type'=>'double(5,2)', 'label'=>'EvaluationScore', 'enabled'=>'1', 'position'=>85, 'notnull'=>0, 'visible'=>1),
        'evaluation_comments' => array('type'=>'text', 'label'=>'EvaluationComments', 'enabled'=>'1', 'position'=>90, 'notnull'=>0, 'visible'=>1),
        'note_private' => array('type'=>'html', 'label'=>'NotePrivate', 'enabled'=>'1', 'position'=>95, 'notnull'=>0, 'visible'=>0),
        'note_public' => array('type'=>'html', 'label'=>'NotePublic', 'enabled'=>'1', 'position'=>96, 'notnull'=>0, 'visible'=>0),
        'date_creation' => array('type'=>'datetime', 'label'=>'DateCreation', 'enabled'=>'1', 'position'=>500, 'notnull'=>1, 'visible'=>0),
        'fk_user_creat' => array('type'=>'integer:User:user/class/user.class.php', 'label'=>'UserAuthor', 'enabled'=>'1', 'position'=>510, 'notnull'=>1, 'visible'=>0, 'foreignkey'=>'user.rowid'),
        'date_modification' => array('type'=>'datetime', 'label'=>'DateModification', 'enabled'=>'1', 'position'=>520, 'notnull'=>0, 'visible'=>0),
        'fk_user_modif' => array('type'=>'integer:User:user/class/user.class.php', 'label'=>'UserModif', 'enabled'=>'1', 'position'=>530, 'notnull'=>0, 'visible'=>0, 'foreignkey'=>'user.rowid'),
    );

    public $rowid;
    public $entity;
    public $ref;
    public $fk_employee;
    public $amount_requested;
    public $purpose;
    public $duration_months;
    public $monthly_salary;
    public $other_loans;
    public $guarantor_name;
    public $guarantor_phone;
    public $guarantor_address;
    public $status; // 0=En attente, 1=En cours évaluation, 2=Approuvé, 3=Rejeté, 4=Annulé
    public $date_request;
    public $date_evaluation;
    public $date_decision;
    public $fk_user_evaluator;
    public $evaluation_score;
    public $evaluation_comments;
    public $rejection_reason;
    public $note_private;
    public $note_public;
    public $date_creation;
    public $fk_user_creat;
    public $date_modification;
    public $fk_user_modif;

    // Statuts des demandes
    const STATUS_PENDING = 0;
    const STATUS_EVALUATING = 1;
    const STATUS_APPROVED = 2;
    const STATUS_REJECTED = 3;
    const STATUS_CANCELLED = 4;

    /**
     * Constructor
     *
     * @param DoliDb $db Database handler
     */
    public function __construct($db)
    {
        global $conf, $langs;

        $this->db = $db;

        if (empty($conf->global->MAIN_SHOW_TECHNICAL_ID) && isset($this->fields['rowid'])) {
            $this->fields['rowid']['visible'] = 0;
        }
        if (!isModEnabled('multicompany') && isset($this->fields['entity'])) {
            $this->fields['entity']['enabled'] = 0;
        }

        // Example to show how to set values of fields definition dynamically
        /*if ($user->rights->mymodule->level1->level2) {
            $this->fields['myfield']['visible'] = 1;
            $this->fields['myfield']['noteditable'] = 0;
        }*/

        // Unset fields that are disabled
        foreach ($this->fields as $key => $val) {
            if (isset($val['enabled']) && empty($val['enabled'])) {
                unset($this->fields[$key]);
            }
        }

        // Translate some data of arrayofkeyval
        if (is_object($langs)) {
            foreach ($this->fields as $key => $val) {
                if (!empty($val['arrayofkeyval']) && is_array($val['arrayofkeyval'])) {
                    foreach ($val['arrayofkeyval'] as $key2 => $val2) {
                        $this->fields[$key]['arrayofkeyval'][$key2] = $langs->trans($val2);
                    }
                }
            }
        }
    }

    /**
     * Create object into database
     *
     * @param  User $user      User that creates
     * @param  bool $notrigger false=launch triggers after, true=disable triggers
     * @return int             <0 if KO, Id of created object if OK
     */
    public function create(User $user, $notrigger = false)
    {
        global $conf;

        // Clean parameters
        if (isset($this->ref)) {
            $this->ref = trim($this->ref);
        }
        if (isset($this->purpose)) {
            $this->purpose = trim($this->purpose);
        }

        // Check parameters
        if (empty($this->ref)) {
            $this->ref = $this->getNextNumRef();
        }

        if (empty($this->date_request)) {
            $this->date_request = dol_now();
        }

        if (empty($this->status)) {
            $this->status = self::STATUS_PENDING;
        }

        if (empty($this->entity)) {
            $this->entity = $conf->entity;
        }

        $this->date_creation = dol_now();
        $this->fk_user_creat = $user->id;

        // Use createCommon which handles all fields defined in $fields automatically
        $result = $this->createCommon($user, $notrigger);

        if ($result > 0) {
            // Ajouter à l'historique
            $this->addToHistory('request_created', $user);
        }

        return $result;
    }

    /**
     * Load object in memory from the database
     *
     * @param int    $id   Id object
     * @param string $ref  Ref
     * @return int         <0 if KO, 0 if not found, >0 if OK
     */
    public function fetch($id, $ref = null)
    {
        // Use fetchCommon which handles all fields defined in $fields automatically
        return $this->fetchCommon($id, $ref);
    }

    /**
     * Return next reference
     *
     * @return string
     */
    public function getNextNumRef()
    {
        // Générer directement la prochaine référence DP sans utiliser le système de masque Dolibarr
        $sql = "SELECT MAX(CAST(SUBSTRING(ref, 3) AS UNSIGNED)) as max_num FROM ".MAIN_DB_PREFIX."employer_loan_request WHERE ref LIKE 'DP%'";
        $resql = $this->db->query($sql);
        $max_num = 1;

        if ($resql) {
            $obj = $this->db->fetch_object($resql);
            $max_num = ($obj->max_num ? $obj->max_num + 1 : 1);
        }

        $numExample = 'DP'.sprintf('%04d', $max_num);

        return $numExample;
    }

    /**
     * Get next value for mask
     *
     * @param string $mask Mask to use
     * @return string      Next value
     */
    public function getNextValue($mask)
    {
        global $conf;

        include_once DOL_DOCUMENT_ROOT.'/core/lib/functions2.lib.php';

        return get_next_value($this->db, $mask, $this->table_element, 'ref', '', null, $this->date_request);
    }

    /**
     * Ajouter un événement à l'historique
     *
     * @param string $event_type Type d'événement
     * @param User   $user       Utilisateur
     * @param string $description Description optionnelle
     * @return int               <0 if KO, >0 if OK
     */
    public function addToHistory($event_type, $user, $description = '')
    {
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_history (";
        $sql .= "fk_employee, fk_loan_request, event_type, event_date, amount, description, fk_user_action";
        $sql .= ") VALUES (";
        $sql .= (int)$this->fk_employee.",";
        $sql .= (int)$this->id.",";
        $sql .= "'".$this->db->escape($event_type)."',";
        $sql .= "NOW(),";
        $sql .= (float)$this->amount_requested.",";
        $sql .= "'".$this->db->escape($description)."',";
        $sql .= (int)$user->id;
        $sql .= ")";

        return $this->db->query($sql);
    }

    /**
     * Obtenir le libellé du statut
     *
     * @param int $status Statut
     * @return string     Libellé
     */
    public function getLibStatut($status = null)
    {
        global $langs;

        if (is_null($status)) {
            $status = $this->status;
        }

        $statusLabels = array(
            self::STATUS_PENDING => $langs->trans('Pending'),
            self::STATUS_EVALUATING => $langs->trans('Evaluating'),
            self::STATUS_APPROVED => $langs->trans('Approved'),
            self::STATUS_REJECTED => $langs->trans('Rejected'),
            self::STATUS_CANCELLED => $langs->trans('Cancelled')
        );

        return isset($statusLabels[$status]) ? $statusLabels[$status] : '';
    }

    /**
     * Return the status
     *
     * @param int     $status      Id status
     * @param int     $mode        0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string              Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        if (empty($this->labelStatus) || empty($this->labelStatusShort)) {
            global $langs;
            $this->labelStatus[self::STATUS_PENDING] = $langs->trans('Pending');
            $this->labelStatus[self::STATUS_EVALUATING] = $langs->trans('Evaluating');
            $this->labelStatus[self::STATUS_APPROVED] = $langs->trans('Approved');
            $this->labelStatus[self::STATUS_REJECTED] = $langs->trans('Rejected');
            $this->labelStatus[self::STATUS_CANCELLED] = $langs->trans('Cancelled');
            $this->labelStatusShort[self::STATUS_PENDING] = $langs->trans('Pending');
            $this->labelStatusShort[self::STATUS_EVALUATING] = $langs->trans('Evaluating');
            $this->labelStatusShort[self::STATUS_APPROVED] = $langs->trans('Approved');
            $this->labelStatusShort[self::STATUS_REJECTED] = $langs->trans('Rejected');
            $this->labelStatusShort[self::STATUS_CANCELLED] = $langs->trans('Cancelled');
        }

        $statusType = 'status'.$status;
        if ($status == self::STATUS_APPROVED) {
            $statusType = 'status4';
        }
        if ($status == self::STATUS_REJECTED) {
            $statusType = 'status8';
        }
        if ($status == self::STATUS_CANCELLED) {
            $statusType = 'status9';
        }

        return dolGetStatus($this->labelStatus[$status], $this->labelStatusShort[$status], '', $statusType, $mode);
    }

    /**
     * Update object into database
     *
     * @param  User $user      User that modifies
     * @param  bool $notrigger false=launch triggers after, true=disable triggers
     * @return int             <0 if KO, >0 if OK
     */
    public function update(User $user, $notrigger = false)
    {
        return $this->updateCommon($user, $notrigger);
    }

    /**
     * Delete object in database
     *
     * @param User $user       User that deletes
     * @param bool $notrigger  false=launch triggers after, true=disable triggers
     * @return int             <0 if KO, >0 if OK
     */
    public function delete(User $user, $notrigger = false)
    {
        return $this->deleteCommon($user, $notrigger);
    }

    /**
     * Load the info information in the object
     *
     * @param  int    $id       Id of object
     * @return void
     */
    public function info($id)
    {
        $sql = "SELECT rowid, date_creation as datec, fk_user_creat as fk_user_author, date_modification as datem,";
        $sql .= " fk_user_modif as fk_user_modif";
        $sql .= " FROM ".MAIN_DB_PREFIX.$this->table_element." as t";
        $sql .= " WHERE t.rowid = ".((int) $id);

        $result = $this->db->query($sql);
        if ($result) {
            if ($this->db->num_rows($result)) {
                $obj = $this->db->fetch_object($result);
                $this->id = $obj->rowid;
                if (!empty($obj->fk_user_author)) {
                    $cuser = new User($this->db);
                    $cuser->fetch($obj->fk_user_author);
                    $this->user_creation = $cuser;
                    $this->user_creation_id = $obj->fk_user_author;
                }

                if (!empty($obj->fk_user_modif)) {
                    $muser = new User($this->db);
                    $muser->fetch($obj->fk_user_modif);
                    $this->user_modification = $muser;
                    $this->user_modification_id = $obj->fk_user_modif;
                }

                $this->date_creation     = $this->db->jdate($obj->datec);
                $this->date_modification = $this->db->jdate($obj->datem);
            }

            $this->db->free($result);
        } else {
            dol_print_error($this->db);
        }
    }

    /**
     * Initialise object with example values
     * Id must be 0 if object instance is a specimen
     *
     * @return void
     */
    public function initAsSpecimen()
    {
        // Set here init that are not commonf fields
        // $this->property1 = ...
        // $this->property2 = ...

        $this->initAsSpecimenCommon();
    }

    /**
     * 	Create an array of lines
     *
     * 	@return array|int		array of lines if OK, <0 if KO
     */
    public function getLinesArray()
    {
        $this->lines = array();

        $objectline = new EmployerLoanRequestLine($this->db);
        $result = $objectline->fetchAll('ASC', 'position', 0, 0, array('customsql'=>'fk_employerloan_request = '.((int) $this->id)));

        if (is_numeric($result)) {
            $this->error = $objectline->error;
            $this->errors = $objectline->errors;
            return $result;
        } else {
            $this->lines = $result;
            return $this->lines;
        }
    }



    /**
     *  Return a link to the object card (with optionaly the picto)
     *
     *  @param	int		$withpicto					Include picto in link (0=No picto, 1=Include picto into link, 2=Only picto)
     *  @param	string	$option						On what the link point to ('nolink', ...)
     *  @param	int		$notooltip					1=Disable tooltip
     *  @param	string	$morecss					Add more css on link
     *  @param	int		$save_lastsearch_value		-1=Auto, 0=No save of lastsearch_values when clicking, 1=Save lastsearch_values whenclicking
     *  @return	string								HTML
     */
    public function getNomUrl($withpicto = 0, $option = '', $notooltip = 0, $morecss = '', $save_lastsearch_value = -1)
    {
        global $conf, $langs, $hookmanager;

        if (!empty($conf->dol_no_mouse_hover)) {
            $notooltip = 1; // Force disable tooltips
        }

        $result = '';

        $label = img_picto('', $this->picto).' <u>'.$langs->trans("LoanRequest").'</u>';
        if (isset($this->status)) {
            $label .= ' '.$this->getLibStatut(5);
        }
        $label .= '<br>';
        $label .= '<b>'.$langs->trans('Ref').':</b> '.$this->ref.'<br>';
        $label .= '<b>'.$langs->trans('Amount').':</b> '.price($this->amount_requested).'<br>';

        $url = dol_buildpath('/employerloan/loan_request.php', 1).'?id='.$this->id;

        if ($option != 'nolink') {
            // Add param to save lastsearch_values or not
            $add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
            if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
                $add_save_lastsearch_values = 1;
            }
            if ($add_save_lastsearch_values) {
                $url .= '&save_lastsearch_values=1';
            }
        }

        $linkclose = '';
        if (empty($notooltip)) {
            if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
                $label = $langs->trans("ShowLoanRequest");
                $linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
            }
            $linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
            $linkclose .= ' class="classfortooltip'.($morecss ? ' '.$morecss : '').'"';
        } else {
            $linkclose = ($morecss ? ' class="'.$morecss.'"' : '');
        }

        if ($option == 'nolink') {
            $linkstart = '<span';
        } else {
            $linkstart = '<a href="'.$url.'"';
        }
        $linkstart .= $linkclose.'>';
        if ($option == 'nolink') {
            $linkend = '</span>';
        } else {
            $linkend = '</a>';
        }

        $result .= $linkstart;

        if (empty($this->showphoto_on_popup)) {
            if ($withpicto) {
                $result .= img_object(($notooltip ? '' : $label), ($this->picto ? $this->picto : 'generic'), ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
            }
        } else {
            if ($withpicto) {
                require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';

                list($class, $module) = explode('@', $this->picto);
                $upload_dir = $conf->$module->multidir_output[$conf->entity]."/$class/".dol_sanitizeFileName($this->ref);
                $filearray = dol_dir_list($upload_dir, "files", 0, '', '(\.meta|_preview.*\.png)$', $conf->global->GED_SORT_FIELD, SORT_ASC, 1);
                if (count($filearray)) {
                    $filename = $filearray[0]['name'];
                    $origfile = $upload_dir.'/'.$filename;
                    $file = $upload_dir.'/'.$filename;
                }
                if (!empty($filename)) {
                    $result .= '<div class="floatleft inline-block valignmiddle divphotoref"><div class="photoref"><img class="photo'.$module.'" alt="No photo" border="0" src="'.DOL_URL_ROOT.'/viewimage.php?modulepart='.$module.'&entity='.$conf->entity.'&file='.urlencode($class.'/'.$this->ref.'/'.$filename).'"></div></div>';
                } else {
                    $result .= img_object(($notooltip ? '' : $label), ($this->picto ? $this->picto : 'generic'), ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
                }
            }
        }

        if ($withpicto != 2) {
            $result .= $this->ref;
        }

        $result .= $linkend;
        //if ($withpicto != 2) $result.=(($addlabel && $this->label) ? $sep . dol_trunc($this->label, ($addlabel > 1 ? $addlabel : 0)) : '');

        global $action, $hookmanager;
        $hookmanager->initHooks(array('employerloan_requestdao'));
        $parameters = array('id'=>$this->id, 'getnomurl'=>$result);
        $reshook = $hookmanager->executeHooks('getNomUrl', $parameters, $this, $action); // Note that $action and $object may have been modified by some hooks
        if ($reshook > 0) {
            $result = $hookmanager->resPrint;
        } else {
            $result .= $hookmanager->resPrint;
        }

        return $result;
    }
}
