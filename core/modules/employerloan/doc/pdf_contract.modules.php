<?php
/**
 * Modèle de contrat de prêt PDF
 */

require_once DOL_DOCUMENT_ROOT.'/core/modules/employerloan/modules_employerloan.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/company.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';

/**
 * Classe pour générer le contrat de prêt en PDF
 */
class pdf_contract extends ModeleEmployerLoan
{
    /**
     * @var string model name
     */
    public $name = 'contract';
    
    /**
     * @var string model description (short)
     */
    public $description = "Contrat de prêt entre employeur et salarié";
    
    /**
     * @var int Save the name only of the file
     */
    public $type = 'pdf';
    
    /**
     * @var array Minimum version of PHP required by module.
     */
    public $phpmin = array(7, 0);
    
    /**
     * @var string page_largeur
     */
    public $page_largeur;
    
    /**
     * @var string page_hauteur
     */
    public $page_hauteur;
    
    /**
     * @var array format
     */
    public $format;
    
    /**
     * @var int marge_gauche
     */
    public $marge_gauche;
    
    /**
     * @var int marge_droite
     */
    public $marge_droite;
    
    /**
     * @var int marge_haute
     */
    public $marge_haute;
    
    /**
     * @var int marge_basse
     */
    public $marge_basse;
    
    /**
     * Constructor
     *
     * @param DoliDB $db Database handler
     */
    public function __construct($db)
    {
        global $conf, $langs, $mysoc;
        
        $this->db = $db;
        
        $this->name = "contract";
        $this->description = $langs->trans('ContractLoanTemplate');
        
        // Page size for A4 format
        $this->type = 'pdf';
        $formatarray = pdf_getFormat();
        $this->page_largeur = $formatarray['width'];
        $this->page_hauteur = $formatarray['height'];
        $this->format = array($this->page_largeur, $this->page_hauteur);
        $this->marge_gauche = isset($conf->global->MAIN_PDF_MARGIN_LEFT) ? $conf->global->MAIN_PDF_MARGIN_LEFT : 10;
        $this->marge_droite = isset($conf->global->MAIN_PDF_MARGIN_RIGHT) ? $conf->global->MAIN_PDF_MARGIN_RIGHT : 10;
        $this->marge_haute = isset($conf->global->MAIN_PDF_MARGIN_TOP) ? $conf->global->MAIN_PDF_MARGIN_TOP : 10;
        $this->marge_basse = isset($conf->global->MAIN_PDF_MARGIN_BOTTOM) ? $conf->global->MAIN_PDF_MARGIN_BOTTOM : 10;
    }
    
    /**
     * Function to build pdf onto disk
     *
     * @param EmployerLoan $object Object to generate
     * @param Translate $outputlangs Lang output object
     * @param string $srctemplatepath Full path of source filename for generator using a template file
     * @param int $hidedetails Do not show line details
     * @param int $hidedesc Do not show desc
     * @param int $hideref Do not show ref
     * @return int 1=OK, 0=KO
     */
    public function write_file($object, $outputlangs, $srctemplatepath = '', $hidedetails = 0, $hidedesc = 0, $hideref = 0)
    {
        global $user, $langs, $conf, $mysoc, $db, $hookmanager;
        
        if (!is_object($outputlangs)) $outputlangs = $langs;
        
        $outputlangs->charset_output = 'UTF-8';
        
        // Load translation files required by the page
        $outputlangs->loadLangs(array("main", "dict", "companies", "employerloan"));
        
        $nblines = count($object->lines);
        
        if ($conf->employerloan->dir_output) {
            $object->fetch_thirdparty();
            
            $dir = $conf->employerloan->dir_output;
            $file = $dir . "/contract_" . $object->ref . ".pdf";
            
            if (!file_exists($dir)) {
                if (dol_mkdir($dir) < 0) {
                    $this->error = $langs->transnoentities("ErrorCanNotCreateDir", $dir);
                    return 0;
                }
            }
            
            if (file_exists($dir)) {
                // Create pdf instance
                $pdf = pdf_getInstance($this->format);
                $default_font_size = pdf_getPDFFontSize($outputlangs);
                $pdf->SetAutoPageBreak(1, 0);
                
                if (class_exists('TCPDF')) {
                    $pdf->setPrintHeader(false);
                    $pdf->setPrintFooter(false);
                }
                $pdf->SetFont(pdf_getPDFFont($outputlangs), '', $default_font_size);
                
                $pdf->Open();
                $pagenb = 0;
                
                $pdf->SetDrawColor(128, 128, 128);
                
                $pdf->SetTitle($outputlangs->convToOutputCharset($object->ref));
                $pdf->SetSubject($outputlangs->transnoentities("LoanContract"));
                $pdf->SetCreator("Dolibarr " . DOL_VERSION);
                $pdf->SetAuthor($outputlangs->convToOutputCharset($user->getFullName($outputlangs)));
                $pdf->SetKeyWords($outputlangs->convToOutputCharset($object->ref) . " " . $outputlangs->transnoentities("LoanContract"));
                if (getDolGlobalString('MAIN_DISABLE_PDF_COMPRESSION')) $pdf->SetCompression(false);
                
                $pdf->SetMargins($this->marge_gauche, $this->marge_haute, $this->marge_droite);
                
                // Add page
                $pdf->AddPage();
                if (!empty($tplidx)) $pdf->useTemplate($tplidx);
                $pagenb++;
                
                $top_shift = $this->_pagehead($pdf, $object, 1, $outputlangs);
                $pdf->SetFont('', '', $default_font_size - 1);
                
                $tab_top = 90;
                $tab_top_newpage = (!getDolGlobalInt('MAIN_PDF_DONOTREPEAT_HEAD') ? 42 : 10);
                $tab_height = $this->page_hauteur - $tab_top - $this->marge_basse;
                $tab_height_newpage = $this->page_hauteur - $tab_top_newpage - $this->marge_basse;
                
                // Contenu du contrat
                $this->_pagefoot($pdf, $object, $outputlangs, 1);
                
                if (method_exists($pdf, 'AliasNbPages')) $pdf->AliasNbPages();
                
                $pdf->Close();
                
                $pdf->Output($file, 'F');
                
                // Add pdfgeneration hook
                $hookmanager->initHooks(array('pdfgeneration'));
                $parameters = array('file'=>$file, 'object'=>$object, 'outputlangs'=>$outputlangs);
                global $action;
                $reshook = $hookmanager->executeHooks('afterPDFCreation', $parameters, $this, $action);
                
                dolChmod($file);
                
                $this->result = array('fullpath'=>$file);
                
                return 1;
            } else {
                $this->error = $langs->transnoentities("ErrorCanNotCreateDir", $dir);
                return 0;
            }
        }
        
        $this->error = $langs->transnoentities("ErrorConstantNotDefined", "EMPLOYERLOAN_OUTPUTDIR");
        return 0;
    }
    
    /**
     * Show top header of page.
     *
     * @param TCPDF $pdf Object PDF
     * @param EmployerLoan $object Object to show
     * @param int $showaddress 0=no, 1=yes
     * @param Translate $outputlangs Object lang for output
     * @return int top shift of PDF
     */
    protected function _pagehead(&$pdf, $object, $showaddress, $outputlangs)
    {
        global $conf, $langs, $mysoc;
        
        $default_font_size = pdf_getPDFFontSize($outputlangs);
        
        pdf_pagehead($pdf, $outputlangs, $this->page_hauteur);
        
        $pdf->SetTextColor(0, 0, 60);
        $pdf->SetFont('', 'B', $default_font_size + 3);
        
        $w = 110;
        
        $posy = $this->marge_haute;
        $posx = $this->page_largeur - $this->marge_droite - $w;
        
        $pdf->SetXY($this->marge_gauche, $posy);
        
        // Logo
        if ($mysoc->logo_small && is_readable($conf->mycompany->dir_output.'/logos/thumbs/'.$mysoc->logo_small)) {
            $logo = $conf->mycompany->dir_output.'/logos/thumbs/'.$mysoc->logo_small;
        } elseif ($mysoc->logo && is_readable($conf->mycompany->dir_output.'/logos/'.$mysoc->logo)) {
            $logo = $conf->mycompany->dir_output.'/logos/'.$mysoc->logo;
        }
        if (!empty($logo)) {
            $pdf->Image($logo, $this->marge_gauche, $posy, 0, 24);
        }
        
        $pdf->SetFont('', 'B', $default_font_size + 3);
        $pdf->SetXY($posx, $posy);
        $pdf->SetTextColor(0, 0, 60);
        $title = $outputlangs->transnoentities("LoanContract");
        $pdf->MultiCell($w, 3, $title, '', 'R');
        
        $pdf->SetFont('', 'B', $default_font_size);
        
        $posy += 5;
        $pdf->SetXY($posx, $posy);
        $pdf->SetTextColor(0, 0, 60);
        $pdf->MultiCell($w, 4, $outputlangs->transnoentities("Ref")." : " . $outputlangs->convToOutputCharset($object->ref), '', 'R');
        
        $posy += 1;
        $pdf->SetXY($posx, $posy);
        $pdf->SetTextColor(0, 0, 60);
        $pdf->MultiCell($w, 4, $outputlangs->transnoentities("Date")." : " . dol_print_date($object->datestart, "day", false, $outputlangs, true), '', 'R');
        
        if ($object->thirdparty->code_client) {
            $posy += 4;
            $pdf->SetXY($posx, $posy);
            $pdf->SetTextColor(0, 0, 60);
            $pdf->MultiCell($w, 3, $outputlangs->transnoentities("CustomerCode")." : " . $outputlangs->transnoentities($object->thirdparty->code_client), '', 'R');
        }
        
        $pdf->SetFont('', '', $default_font_size - 1);
        
        return $posy;
    }
    
    /**
     * Show footer of page. Need this->emetteur object
     *
     * @param TCPDF $pdf PDF
     * @param EmployerLoan $object Object to show
     * @param Translate $outputlangs Object lang for output
     * @param int $hidefreetext 1=Hide free text
     * @return int Return height of bottom margin including footer text
     */
    protected function _pagefoot(&$pdf, $object, $outputlangs, $hidefreetext = 0)
    {
        $showdetails = getDolGlobalInt('MAIN_GENERATE_DOCUMENTS_SHOW_FOOT_DETAILS', 0);
        return pdf_pagefoot($pdf, $outputlangs, 'EMPLOYERLOAN_FREE_TEXT', $this->emetteur, $this->marge_basse, $this->marge_gauche, $this->page_hauteur, $object, $showdetails, $hidefreetext);
    }
}
