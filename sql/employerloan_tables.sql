-- Table principale des crédits salariés
CREATE TABLE llx_employer_loan (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    entity int(11) NOT NULL DEFAULT 1,
    datec datetime DEFAULT NULL,
    tms timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    label varchar(80) NOT NULL,
    fk_bank int(11) DEFAULT NULL,
    capital double(24,8) NOT NULL DEFAULT 0.********,
    insurance_amount double(24,8) DEFAULT 0.********,
    datestart date DEFAULT NULL,
    dateend date DEFAULT NULL,
    nbterm double DEFAULT NULL,
    rate double NOT NULL,
    note_private text DEFAULT NULL,
    note_public text DEFAULT NULL,
    capital_position double(24,8) DEFAULT 0.********,
    date_position date DEFAULT NULL,
    paid smallint(6) NOT NULL DEFAULT 0,
    accountancy_account_capital varchar(32) DEFAULT NULL,
    accountancy_account_insurance varchar(32) DEFAULT NULL,
    accountancy_account_interest varchar(32) DEFAULT NULL,
    fk_projet int(11) DEFAULT NULL,
    fk_user_author int(11) DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    active tinyint(4) NOT NULL DEFAULT 1,
    fk_employee int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_employee (fk_employee),
    CONSTRAINT fk_employer_loan_employee FOREIGN KEY (fk_employee) REFERENCES llx_user(rowid)
        ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table échéancier
CREATE TABLE llx_employer_loan_schedule (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_loan int(11) DEFAULT NULL,
    datec datetime DEFAULT NULL,
    tms timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    datep datetime DEFAULT NULL,
    amount_capital double(24,8) DEFAULT 0.********,
    amount_insurance double(24,8) DEFAULT 0.********,
    amount_interest double(24,8) DEFAULT 0.********,
    fk_typepayment int(11) NOT NULL,
    num_payment varchar(50) DEFAULT NULL,
    note_private text DEFAULT NULL,
    note_public text DEFAULT NULL,
    fk_bank int(11) NOT NULL,
    fk_payment_loan int(11) DEFAULT NULL,
    fk_user_creat int(11) DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_loan (fk_loan),
    CONSTRAINT fk_employer_loan_schedule_loan FOREIGN KEY (fk_loan) REFERENCES llx_employer_loan(rowid)
        ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table paiements
CREATE TABLE llx_employer_payment_loan (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_loan int(11) DEFAULT NULL,
    datec datetime DEFAULT NULL,
    tms timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    datep datetime DEFAULT NULL,
    amount_capital double(24,8) DEFAULT 0.********,
    amount_insurance double(24,8) DEFAULT 0.********,
    amount_interest double(24,8) DEFAULT 0.********,
    fk_typepayment int(11) NOT NULL,
    num_payment varchar(50) DEFAULT NULL,
    note_private text DEFAULT NULL,
    note_public text DEFAULT NULL,
    fk_bank int(11) NOT NULL,
    fk_user_creat int(11) DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_loan (fk_loan),
    CONSTRAINT fk_employer_payment_loan_loan FOREIGN KEY (fk_loan) REFERENCES llx_employer_loan(rowid)
        ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 