<?php
/* TVI
 * Copyright (C) 2015	<PERSON>lor<PERSON> 		<<EMAIL>>
 * Copyright (C) 2020   Maxime DEMAREST     <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *  \file htdocs/loan/calcmens.php
 *  \ingroup    loan
 *  \brief File to calculate loan monthly payments
 */

if (!defined('NOTOKENRENEWAL')) {
	define('NOTOKENRENEWAL', '1'); // Disables token renewal
}
if (!defined('NOREQUIREMENU')) {
	define('NOREQUIREMENU', '1');
}
if (!defined('NOREQUIREAJAX')) {
	define('NOREQUIREAJAX', '1');
}

// Load Dolibarr environment
$res = 0;
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
if (!$res) $res = @include "../../main.inc.php";
if (!$res) $res = @include "../../../main.inc.php";
if (!$res) die("Include of main fails");
require DOL_DOCUMENT_ROOT.'/core/lib/loan.lib.php';

$mens = price2num(GETPOST('mens'));
$capital = price2num(GETPOST('capital'));
$rate = price2num(GETPOST('rate'));
$echance = GETPOST('echeance', 'int');
$nbterm = GETPOST('nbterm', 'int');

top_httphead();

$output = array();

$output = loanCalcMonthlyPayment($mens, $capital, $rate, $echance, $nbterm);

echo json_encode($output);
