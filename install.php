<?php
// Script d'installation pour définir MAIN_MODULE_CUSTOMDIR automatiquement

define('DOL_DOCUMENT_ROOT', dirname(__DIR__, 2)); // racine <PERSON> = sinedtyi
require_once DOL_DOCUMENT_ROOT . '/master.inc.php';
require_once DOL_DOCUMENT_ROOT . '/core/lib/admin.lib.php'; // Ajouté pour dolibarr_set_const

$const = 'MAIN_MODULE_CUSTOMDIR';
$val = DOL_DOCUMENT_ROOT . '/custom';

if (empty($conf->global->$const)) {
    dolibarr_set_const($db, $const, $val, 'chaine', 0, '', $conf->entity);
    echo "Constante $const définie automatiquement à $val<br>";
} else {
    echo "Constante $const déjà définie à " . $conf->global->$const . "<br>";
} 