<?php
require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

/**
 *		Class to manage payments of employer loans
 */
class EmployerPaymentLoan extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'employer_payment_loan';

    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'employer_payment_loan';

    /**
     * @var int ID
     */
    public $rowid;

    /**
     * @var int Loan ID
     */
    public $fk_loan;

    /**
     * @var string Create date
     */
    public $datec;
    public $tms;

    /**
     * @var string Payment date
     */
    public $datep;

    public $amounts = array(); // Array of amounts
    public $amount_capital; // Total amount of payment
    public $amount_insurance;
    public $amount_interest;

    /**
     * @var int Payment Type ID
     */
    public $fk_typepayment;

    /**
     * @var int Payment ID
     */
    public $num_payment;

    /**
     * @var int Bank ID
     */
    public $fk_bank;

    /**
     * @var int Bank ID
     */
    public $fk_user_creat;

    /**
     * @var int User ID
     */
    public $fk_user_modif;

    /**
     * @deprecated
     * @see $amount, $amounts
     */
    public $total;

    public $type_code;
    public $type_label;

    public $note_private;
    public $note_public;

    public function __construct($db)
    {
        $this->db = $db;
    }

    public function fetch($id)
    {
        $sql = "SELECT * FROM ".MAIN_DB_PREFIX."employer_payment_loan WHERE rowid = ".((int) $id);
        $resql = $this->db->query($sql);
        if ($resql) {
            if ($this->db->num_rows($resql)) {
                $obj = $this->db->fetch_object($resql);
                foreach ($obj as $key => $value) {
                    $this->$key = $value;
                }
                $this->db->free($resql);
                return 1;
            }
            $this->db->free($resql);
            return 0;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    public function create($user)
    {
        $now = dol_now();
        $this->db->begin();
        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_payment_loan (fk_loan, datec, datep, amount_capital, amount_insurance, amount_interest, fk_typepayment, num_payment, note_private, note_public, fk_bank, fk_user_creat, fk_user_modif) VALUES (";
        $sql .= "".((int) $this->fk_loan).", ";
        $sql .= "'".$this->db->idate($now)."', ";
        $sql .= "'".$this->db->idate($this->datep)."', ";
        $sql .= "".price2num($this->amount_capital).", ";
        $sql .= "".price2num($this->amount_insurance).", ";
        $sql .= "".price2num($this->amount_interest).", ";
        $sql .= "".((int) $this->fk_typepayment).", ";
        $sql .= "'".$this->db->escape($this->num_payment)."', ";
        $sql .= "'".$this->db->escape($this->note_private)."', ";
        $sql .= "'".$this->db->escape($this->note_public)."', ";
        $sql .= "".((int) $this->fk_bank).", ";
        $sql .= "".((int) $user->id).", ";
        $sql .= "NULL"; // fk_user_modif
        $sql .= ")";
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->rowid = $this->db->last_insert_id(MAIN_DB_PREFIX."employer_payment_loan");
            $this->db->commit();
            return $this->rowid;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    public function update($user)
    {
        $this->db->begin();
        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_payment_loan SET ";
        $sql .= "fk_loan=".((int) $this->fk_loan).", ";
        $sql .= "datep='".$this->db->idate($this->datep)."', ";
        $sql .= "amount_capital=".price2num($this->amount_capital).", ";
        $sql .= "amount_insurance=".price2num($this->amount_insurance).", ";
        $sql .= "amount_interest=".price2num($this->amount_interest).", ";
        $sql .= "fk_typepayment=".((int) $this->fk_typepayment).", ";
        $sql .= "num_payment='".$this->db->escape($this->num_payment)."', ";
        $sql .= "note_private='".$this->db->escape($this->note_private)."', ";
        $sql .= "note_public='".$this->db->escape($this->note_public)."', ";
        $sql .= "fk_bank=".((int) $this->fk_bank).", ";
        $sql .= "fk_user_modif=".((int) $user->id)." ";
        $sql .= "WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    public function delete($user)
    {
        $this->db->begin();
        $sql = "DELETE FROM ".MAIN_DB_PREFIX."employer_payment_loan WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Add payment line into bank account
     *
     * @param	User	$user				User making payment
     * @param	int		$fk_loan			Id of loan
     * @param	string	$mode				'payment_loan' or 'loan'
     * @param	string	$label				Label to use in bank
     * @param	int		$accountid			Id of bank account
     * @param	string	$emetteur_nom		Name of transmitter
     * @param	string	$emetteur_banque	Name of bank
     * @return	int							<0 if KO, >0 if OK
     */
    public function addPaymentToBank($user, $fk_loan, $mode, $label, $accountid, $emetteur_nom, $emetteur_banque)
    {
        global $conf;

        $error = 0;
        $this->db->begin();

        if (isModEnabled("banque")) {
            require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';

            $acc = new Account($this->db);
            $acc->fetch($accountid);

            $total = $this->amount_capital;
            if ($mode == 'payment_loan') {
                $total = -$total;
            }

            // Insert payment into llx_bank
            $bank_line_id = $acc->addline(
                $this->datep,
                $this->fk_typepayment, // Payment mode ID or code ("CHQ or VIR for example")
                $label,
                $total,
                $this->num_payment,
                '',
                $user,
                $emetteur_nom,
                $emetteur_banque
            );

            // Update fk_bank in payment. So we know to which bank transaction the payment is linked.
            if ($bank_line_id > 0) {
                $result = $this->update_fk_bank($bank_line_id);
                if ($result <= 0) {
                    $error++;
                    dol_print_error($this->db);
                }

                // Add link 'payment' in bank_url between payment and bank transaction
                if (!$error) {
                    $url = '';
                    if ($mode == 'payment_loan') {
                        $url = DOL_URL_ROOT.'/loan/payment/card.php?id=';
                    }
                    if ($mode == 'loan') {
                        $url = DOL_URL_ROOT.'/loan/card.php?id=';
                    }
                    if ($url) {
                        $result = $acc->add_url_line($bank_line_id, $this->rowid, $url, '(EmployerLoanPayment)', $mode);
                        if ($result <= 0) {
                            $error++;
                            dol_print_error($this->db);
                        }
                    }
                }

                // Add link 'loan' in bank_url between loan and bank transaction (for each loan concerned by payment)
                if (!$error) {
                    $result = $acc->add_url_line($bank_line_id, $fk_loan, DOL_URL_ROOT.'/custom/employerloan/card.php?id=', $this->num_payment, 'employer_loan');
                    if ($result <= 0) {
                        $error++;
                        dol_print_error($this->db);
                    }
                }
            } else {
                $this->error = $acc->error;
                $error++;
            }
        }

        if (!$error) {
            $this->db->commit();
            return 1;
        } else {
            $this->db->rollback();
            return -1;
        }
    }

    /**
     *  Update link between payment and bank transaction
     *
     *  @param	int		$id_bank	Id bank account
     *  @return	int					<0 if KO, >0 if OK
     */
    public function update_fk_bank($id_bank)
    {
        $sql = 'UPDATE '.MAIN_DB_PREFIX.'employer_payment_loan set fk_bank = '.((int) $id_bank);
        $sql .= ' WHERE rowid = '.((int) $this->rowid);

        dol_syslog(get_class($this).'::update_fk_bank', LOG_DEBUG);
        $result = $this->db->query($sql);
        if ($result) {
            $this->fk_bank = $id_bank;
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            dol_print_error($this->db);
            return -1;
        }
    }

    /**
     * Return clicable name (with picto eventually)
     *
     * @param int $withpicto 0=No picto, 1=Include picto into link, 2=Only picto
     * @param string $option Where point the link ('card', 'nolink', ...)
     * @param int $notooltip 1=Disable tooltip
     * @param string $morecss Add more css on link
     * @param int $save_lastsearch_value -1=Auto, 0=No save of lastsearch_values when clicking, 1=Save lastsearch_values whenclicking
     * @return string HTML
     */
    public function getNomUrl($withpicto = 0, $option = '', $notooltip = 0, $morecss = '', $save_lastsearch_value = -1)
    {
        global $conf, $langs, $hookmanager;

        if (!empty($conf->dol_no_mouse_hover)) {
            $notooltip = 1; // Force disable tooltips
        }

        $result = '';

        $label = img_picto('', 'payment').' <u>'.$langs->trans("LoanPayment").'</u>';
        $label .= '<br>';
        $label .= '<b>'.$langs->trans('Ref').':</b> '.$this->rowid.'<br>';
        $label .= '<b>'.$langs->trans('Date').':</b> '.dol_print_date($this->datep, 'day').'<br>';
        $label .= '<b>'.$langs->trans('Amount').':</b> '.price($this->amount_capital + $this->amount_interest + $this->amount_insurance).'<br>';

        $url = DOL_URL_ROOT.'/custom/employerloan/payment/card.php?id='.$this->rowid;

        if ($option != 'nolink') {
            // Add param to save lastsearch_values or not
            $add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
            if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
                $add_save_lastsearch_values = 1;
            }
            if ($add_save_lastsearch_values) {
                $url .= '&save_lastsearch_values=1';
            }
        }

        $linkclose = '';
        if (empty($notooltip)) {
            if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
                $label = $langs->trans("ShowPayment");
                $linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
            }
            $linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
            $linkclose .= ' class="classfortooltip'.($morecss ? ' '.$morecss : '').'"';
        } else {
            $linkclose = ($morecss ? ' class="'.$morecss.'"' : '');
        }

        $linkstart = '<a href="'.$url.'"';
        $linkstart .= $linkclose.'>';
        $linkend = '</a>';

        $result .= $linkstart;
        if ($withpicto) {
            $result .= img_object(($notooltip ? '' : $label), 'payment', ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
        }
        if ($withpicto != 2) {
            $result .= $this->rowid;
        }
        $result .= $linkend;

        return $result;
    }

    /**
     * Return the status
     *
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function getLibStatut($mode = 0)
    {
        global $langs;

        $statusType = 'status6';
        $labelStatus = $langs->trans('Validated');
        $labelStatusShort = $langs->trans('Validated');

        return dolGetStatus($labelStatus, $labelStatusShort, '', $statusType, $mode);
    }

    /**
     * Return the status
     *
     * @param int $status Id status
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @return string Label of status
     */
    public function LibStatut($status, $mode = 0)
    {
        return $this->getLibStatut($mode);
    }
}