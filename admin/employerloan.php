<?php
/* Copyright (C) 2014-2017  <PERSON>	<<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 */

/**
 * \file       htdocs/admin/employerloan.php
 * \ingroup    employerloan
 * \brief      Setup page to configure employer loan module (crédit salarié)
 */

// Load Do<PERSON>barr environment
require '../../../main.inc.php';

// Class
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';
if (isModEnabled('accounting')) {
	require_once DOL_DOCUMENT_ROOT.'/core/class/html.formaccounting.class.php';
}

// Load translation files required by the page
$langs->loadLangs(array('admin', 'employerloan@employerloan'));
$langs->load('employerloan@employerloan');

// Security check
if (!$user->admin) {
	accessforbidden();
}

$action = GETPOST('action', 'aZ09');
$tab = GETPOST('tab', 'alpha') ? GETPOST('tab', 'alpha') : 'setup';

// Other parameters EMPLOYERLOAN_*
$list = array(
	'EMPLOYERLOAN_ACCOUNTING_ACCOUNT_CAPITAL',
	'EMPLOYERLOAN_ACCOUNTING_ACCOUNT_INTEREST',
	'EMPLOYERLOAN_ACCOUNTING_ACCOUNT_INSURANCE'
);

/*
 * Actions
 */

if ($action == 'update') {
	$error = 0;

	foreach ($list as $constname) {
		$constvalue = GETPOST($constname, 'alpha');

		if (!dolibarr_set_const($db, $constname, $constvalue, 'chaine', 0, '', $conf->entity)) {
			$error++;
		}
	}

	if (!$error) {
		setEventMessages($langs->trans("SetupSaved"), null, 'mesgs');
	} else {
		setEventMessages($langs->trans("Error"), null, 'errors');
	}
}

/*
 * View
 */

llxHeader();

$form = new Form($db);
if (isModEnabled('accounting')) {
	$formaccounting = new FormAccounting($db);
}

$linkback = '<a href="'.DOL_URL_ROOT.'/admin/modules.php?restore_lastsearch_values=1">'.$langs->trans("BackToModuleList").'</a>';
print load_fiche_titre($langs->trans('ConfigEmployerLoan'), $linkback, 'title_setup');

// Configuration tabs
$head = array();
$head[0][0] = $_SERVER["PHP_SELF"].'?tab=setup';
$head[0][1] = $langs->trans('Setup');
$head[0][2] = 'setup';

$head[1][0] = $_SERVER["PHP_SELF"].'?tab=criteria';
$head[1][1] = $langs->trans('EvaluationCriteria');
$head[1][2] = 'criteria';

$head[2][0] = $_SERVER["PHP_SELF"].'?tab=committee';
$head[2][1] = $langs->trans('LoanCommittee');
$head[2][2] = 'committee';

$head[3][0] = $_SERVER["PHP_SELF"].'?tab=about';
$head[3][1] = $langs->trans('About');
$head[3][2] = 'about';

print dol_get_fiche_head($head, $tab, '', -1);

if ($tab == 'setup') {
    // Configuration générale
    print '<form action="'.$_SERVER["PHP_SELF"].'?tab=setup" method="post">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="update">';

    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<td colspan="3">'.$langs->trans('Options').'</td>';
    print "</tr>\n";

    foreach ($list as $key) {
        print '<tr class="oddeven value">';

        // Param
        $label = $langs->trans($key);
        print '<td><label for="' . $key . '">' . $label . '</label></td>';

        // Value
        print '<td>';
        if (isModEnabled('accounting')) {
            print $formaccounting->select_account(getDolGlobalString($key), $key, 1, '', 1, 1);
        } else {
            print '<input type="text" size="20" id="' . $key . '" name="' . $key . '" value="' . getDolGlobalString($key) . '">';
        }
        print '</td></tr>';
    }

    print '</table>';
    print '<br><div style="text-align:center"><input type="submit" class="button button-edit" name="button" value="'.$langs->trans('Modify').'"></div>';
    print '</form>';

} elseif ($tab == 'criteria') {
    // Include criteria setup
    include_once DOL_DOCUMENT_ROOT.'/custom/employerloan/criteria_setup_content.php';

} elseif ($tab == 'committee') {
    // Include committee setup
    include_once DOL_DOCUMENT_ROOT.'/custom/employerloan/committee_setup_content.php';

} elseif ($tab == 'about') {
    // Include about page
    include_once DOL_DOCUMENT_ROOT.'/custom/employerloan/admin/aboutemployerloan.php';
}

print dol_get_fiche_end();

// End of page
llxFooter();
$db->close();
