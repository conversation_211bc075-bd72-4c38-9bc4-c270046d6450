<?php
/* Copyright (C) 2024 NextGestion
 *
 * Script de diagnostic pour le module EmployerLoan
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

// Security check
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Diagnostic Module EmployerLoan</title></head><body>';
print '<h1>🔧 Diagnostic Module EmployerLoan</h1>';

// 1. Vérifier l'existence de la classe du module
print '<h2>1. Vérification de la classe du module</h2>';
$moduleFile = DOL_DOCUMENT_ROOT.'/custom/employerloan/core/modules/modEmployerLoan.class.php';
if (file_exists($moduleFile)) {
    print '✅ Fichier de classe trouvé : '.$moduleFile.'<br>';
    
    // Inclure et tester la classe
    try {
        include_once $moduleFile;
        if (class_exists('modEmployerLoan')) {
            print '✅ Classe modEmployerLoan existe<br>';
            
            // Tester l'instanciation
            try {
                $module = new modEmployerLoan($db);
                print '✅ Module instancié avec succès<br>';
                print 'Numéro du module : '.$module->numero.'<br>';
                print 'Nom du module : '.$module->name.'<br>';
                print 'Version : '.$module->version.'<br>';
                print 'Description : '.$module->description.'<br>';
            } catch (Exception $e) {
                print '❌ Erreur lors de l\'instanciation : '.$e->getMessage().'<br>';
            }
        } else {
            print '❌ Classe modEmployerLoan n\'existe pas<br>';
        }
    } catch (Exception $e) {
        print '❌ Erreur lors de l\'inclusion : '.$e->getMessage().'<br>';
    }
} else {
    print '❌ Fichier de classe non trouvé : '.$moduleFile.'<br>';
}

// 2. Vérifier les permissions
print '<h2>2. Vérification des permissions</h2>';
if (isset($user->rights->employerloan)) {
    print '✅ Droits employerloan définis<br>';
    if (isset($user->rights->employerloan->read)) {
        print '✅ Droit de lecture : '.($user->rights->employerloan->read ? 'OUI' : 'NON').'<br>';
    }
    if (isset($user->rights->employerloan->write)) {
        print '✅ Droit d\'écriture : '.($user->rights->employerloan->write ? 'OUI' : 'NON').'<br>';
    }
    if (isset($user->rights->employerloan->delete)) {
        print '✅ Droit de suppression : '.($user->rights->employerloan->delete ? 'OUI' : 'NON').'<br>';
    }
} else {
    print '❌ Droits employerloan non définis<br>';
}

// 3. Vérifier la configuration
print '<h2>3. Vérification de la configuration</h2>';
if (isset($conf->employerloan)) {
    print '✅ Configuration employerloan définie<br>';
    if (isset($conf->employerloan->enabled)) {
        print 'Module activé : '.($conf->employerloan->enabled ? 'OUI' : 'NON').'<br>';
    }
    if (isset($conf->employerloan->dir_output)) {
        print 'Répertoire de sortie : '.$conf->employerloan->dir_output.'<br>';
    }
} else {
    print '❌ Configuration employerloan non définie<br>';
}

// 4. Vérifier les fichiers principaux
print '<h2>4. Vérification des fichiers principaux</h2>';
$files = array(
    'list.php' => 'Liste des prêts',
    'card.php' => 'Fiche prêt',
    'loan_request.php' => 'Demande de crédit',
    'admin/employerloan.php' => 'Configuration',
    'class/employerloan.class.php' => 'Classe principale',
    'class/employerloan_request.class.php' => 'Classe demande',
    'langs/employerloan.lang' => 'Traductions'
);

foreach ($files as $file => $description) {
    $fullPath = DOL_DOCUMENT_ROOT.'/custom/employerloan/'.$file;
    if (file_exists($fullPath)) {
        print '✅ '.$description.' : '.$file.'<br>';
    } else {
        print '❌ '.$description.' manquant : '.$file.'<br>';
    }
}

// 5. Vérifier les tables de base de données
print '<h2>5. Vérification des tables de base de données</h2>';
$tables = array(
    'employer_loan' => 'Table principale des prêts',
    'employer_loan_request' => 'Table des demandes',
    'employer_loan_criteria' => 'Table des critères',
    'employer_loan_evaluation' => 'Table des évaluations',
    'employer_loan_committee' => 'Table du comité',
    'employer_loan_installment' => 'Table des traites',
    'employer_loan_history' => 'Table de l\'historique'
);

foreach ($tables as $table => $description) {
    $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
    $resql = $db->query($sql);
    if ($resql && $db->num_rows($resql) > 0) {
        print '✅ '.$description.' : '.MAIN_DB_PREFIX.$table.'<br>';
    } else {
        print '❌ '.$description.' manquante : '.MAIN_DB_PREFIX.$table.'<br>';
    }
}

// 6. Vérifier les constantes
print '<h2>6. Vérification des constantes</h2>';
$sql = "SELECT name, value FROM ".MAIN_DB_PREFIX."const WHERE name LIKE '%EMPLOYERLOAN%' OR name LIKE '%MAIN_MODULE_EMPLOYERLOAN%'";
$resql = $db->query($sql);
if ($resql) {
    $num = $db->num_rows($resql);
    if ($num > 0) {
        print 'Constantes trouvées :<br>';
        while ($obj = $db->fetch_object($resql)) {
            print '- '.$obj->name.' = '.$obj->value.'<br>';
        }
    } else {
        print '❌ Aucune constante trouvée<br>';
    }
} else {
    print '❌ Erreur lors de la vérification des constantes<br>';
}

// 7. Vérifier les menus
print '<h2>7. Vérification des menus</h2>';
$sql = "SELECT rowid, titre, url, mainmenu, leftmenu FROM ".MAIN_DB_PREFIX."menu WHERE module = 'employerloan' OR url LIKE '%employerloan%'";
$resql = $db->query($sql);
if ($resql) {
    $num = $db->num_rows($resql);
    if ($num > 0) {
        print 'Menus trouvés :<br>';
        while ($obj = $db->fetch_object($resql)) {
            print '- '.$obj->titre.' ('.$obj->url.') - Main: '.$obj->mainmenu.', Left: '.$obj->leftmenu.'<br>';
        }
    } else {
        print '❌ Aucun menu trouvé<br>';
    }
} else {
    print '❌ Erreur lors de la vérification des menus<br>';
}

// 8. Test de chargement des traductions
print '<h2>8. Test des traductions</h2>';
$langs->loadLangs(array("employerloan@employerloan"));
if ($langs->trans('EmployerLoan') != 'EmployerLoan') {
    print '✅ Traductions chargées : '.$langs->trans('EmployerLoan').'<br>';
} else {
    print '❌ Traductions non chargées<br>';
}

// 9. Recommandations
print '<h2>9. Recommandations</h2>';
print '<div style="background-color: #f0f8ff; padding: 10px; border: 1px solid #ccc;">';
print '<h3>Pour activer le module :</h3>';
print '1. Aller dans Configuration > Modules<br>';
print '2. Chercher "EmployerLoan" ou "Prêt salarié"<br>';
print '3. Cliquer sur "Activer"<br>';
print '4. Configurer les permissions utilisateur<br>';
print '5. Installer les tables si nécessaire<br>';
print '<br>';
print '<h3>En cas de problème :</h3>';
print '1. Vérifier les logs d\'erreur Dolibarr<br>';
print '2. Vérifier les permissions de fichiers<br>';
print '3. Vider le cache si nécessaire<br>';
print '4. Redémarrer le serveur web<br>';
print '</div>';

// 10. Actions rapides
print '<h2>10. Actions rapides</h2>';
print '<a href="'.$_SERVER['PHP_SELF'].'?action=clear_cache" style="background-color: #007cba; color: white; padding: 5px 10px; text-decoration: none;">Vider le cache</a> ';
print '<a href="/admin/modules.php" style="background-color: #28a745; color: white; padding: 5px 10px; text-decoration: none;">Aller aux modules</a> ';
print '<a href="/custom/employerloan/admin/employerloan.php" style="background-color: #ffc107; color: black; padding: 5px 10px; text-decoration: none;">Configuration</a>';

// Action pour vider le cache
if (GETPOST('action') == 'clear_cache') {
    // Vider le cache Dolibarr
    if (function_exists('dol_delete_dir_recursive')) {
        $cachedir = DOL_DATA_ROOT.'/admin/temp';
        if (is_dir($cachedir)) {
            dol_delete_dir_recursive($cachedir);
            print '<br><br>✅ Cache vidé avec succès';
        }
    }
}

print '</body></html>';
