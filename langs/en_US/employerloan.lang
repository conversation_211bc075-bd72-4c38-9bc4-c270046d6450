# Dolibarr language file - Source file is UTF-8
# Module EmployerLoan

CHARSET=UTF-8

# Module
Module200000Name=Employee Loans
Module200000Desc=Employee loan management with approval workflow

# Permissions
Permission200001=Read employee loans
Permission200002=Create/modify employee loans
Permission200003=Delete employee loans
Permission200004=Export employee loans
Permission200005=Configure module

# Menu
TopMenuEmployerLoan=Employee Loans
LeftMenuEmployerLoan=Employee Loans
LeftMenuEmployerLoanList=Loans List
LeftMenuEmployerLoanNew=New Loan
LeftMenuEmployerLoanRequests=Credit Requests
LeftMenuEmployerLoanCommitteeDecision=Committee Validation
LeftMenuEmployerLoanSetup=Configuration

# Configuration
ConfigEmployerLoan=Employee Loans module configuration
EmployerLoanSetup=Employee Loans Configuration
EmployerLoanSetupPage=Employee Loans module configuration page

# Objects
EmployerLoan=Employee Loan
EmployerLoans=Employee Loans
NewEmployerLoan=New Employee Loan
EmployerLoanRequest=Credit Request
EmployerLoanRequests=Credit Requests
NewEmployerLoanRequest=New Credit Request

# Main fields
Ref=Reference
Label=Label
Amount=Amount
AmountRequested=Requested Amount
Capital=Capital
DateStart=Start Date
DateEnd=End Date
Duration=Duration
DurationMonths=Duration (months)
Employee=Employee
Purpose=Purpose
Status=Status
Paid=Paid
NotPaid=Not Paid
Rate=Rate
NbTerm=Number of terms
MonthlySalary=Monthly Salary
OtherLoans=Other Loans
Guarantor=Guarantor
GuarantorName=Guarantor Name
GuarantorPhone=Guarantor Phone

# Request statuses
Pending=Pending
Evaluating=Evaluating
UnderReview=Under Review
Approved=Approved
Rejected=Rejected
Cancelled=Cancelled

# Evaluation
EvaluationScore=Evaluation Score
EvaluationComments=Evaluation Comments
Criteria=Criteria
CriteriaCode=Criteria Code
CriteriaLabel=Criteria Label
CriteriaDescription=Criteria Description
CriteriaWeight=Weight
CriteriaMinValue=Minimum Value
CriteriaMaxValue=Maximum Value
CriteriaFormula=Formula
CriteriaPosition=Position

# Committee
Committee=Committee
CommitteeMember=Committee Member
CommitteeMembers=Committee Members
CommitteeRole=Role
CommitteeDecision=Committee Decision
CommitteeVote=Committee Vote
MaxAmount=Maximum Amount

# Committee roles
President=President
Member=Member
Secretary=Secretary
Observer=Observer

# Vote actions
Vote=Vote
YourVote=Your Vote
Approve=Approve
Reject=Reject
Abstain=Abstain
RecordVote=Record Vote
FinalDecision=Final Decision
ApproveRequest=Approve Request
RejectRequest=Reject Request

# Admin tabs
Setup=Setup
EvaluationCriteria=Evaluation Criteria
LoanCommittee=Loan Committee
ValidationComite=Committee Validation

# Actions
NewCriteria=New Criteria
AddCommitteeMember=Add Member
Create=Create
Modify=Modify
Delete=Delete
Add=Add
Cancel=Cancel
Save=Save
Edit=Edit
View=View
List=List

# Confirmation messages
ConfirmApproveRequest=Are you sure you want to approve this request?
ConfirmRejectRequest=Are you sure you want to reject this request?
ConfirmDeleteCriteria=Are you sure you want to delete this criteria?
ConfirmDeleteCommitteeMember=Are you sure you want to delete this committee member?
ConfirmDeleteObject=Are you sure you want to delete this object?

# Success messages
LoanRequestApproved=Credit request approved successfully
LoanRequestRejected=Credit request rejected
CriteriaCreated=Criteria created successfully
CriteriaUpdated=Criteria updated successfully
CriteriaDeleted=Criteria deleted successfully
CriteriaActivated=Criteria activated
CriteriaDisabled=Criteria disabled
CommitteeMemberAdded=Committee member added successfully
CommitteeMemberUpdated=Committee member updated successfully
CommitteeMemberDeleted=Committee member deleted successfully
CommitteeMemberActivated=Committee member activated
CommitteeMemberDisabled=Committee member disabled
VoteRecorded=Vote recorded successfully
ObjectCreated=Object created successfully
ObjectUpdated=Object updated successfully
ObjectDeleted=Object deleted successfully

# Error messages
ErrorCreatingLoan=Error creating loan
ErrorCreatingInstallments=Error creating installments
ErrorRecordingVote=Error recording vote
YouAreNotCommitteeMember=You are not a committee member
NoCriteriaFound=No criteria found
NoCommitteeMemberFound=No committee member found
NoVotesYet=No votes yet
ErrorFieldRequired=Field %s is required

# Contextual help
CriteriaCodeHelp=Unique code to identify the criteria
CriteriaLabelHelp=Displayed label of the criteria
CriteriaDescriptionHelp=Detailed description of the criteria
CriteriaWeightHelp=Weight of the criteria in evaluation (1.0 to 10.0)
CriteriaMinValueHelp=Minimum acceptable value
CriteriaMaxValueHelp=Maximum acceptable value
CriteriaFormulaHelp=Calculation formula (optional)

# Miscellaneous
BackToList=Back to list
BackToModuleList=Back to module list
NoRecordFound=No record found
NoLimit=No limit
Months=months
Days=days
Years=years
Active=Active
Inactive=Inactive
Enabled=Enabled
Disabled=Disabled
Position=Position
Code=Code
Description=Description
Weight=Weight
MinValue=Min Value
MaxValue=Max Value
Formula=Formula
User=User
Role=Role
Comments=Comments
Date=Date
DateCreation=Creation Date
DateModification=Modification Date
Action=Action
Options=Options
General=General
Advanced=Advanced

# Workflow
WorkflowTitle=Request processing workflow
Step1=1. Initial request
Step2=2. Automatic evaluation
Step3=3. Committee review
Step4=4. Final decision
Step5=5. Loan creation (if approved)

# Lists
LoansList=Loans List
LoanRequestsList=Credit Requests List
CriteriaList=Criteria List
CommitteeList=Committee List

# Payments and installments
Payment=Payment
Payments=Payments
Installment=Installment
Installments=Installments
InstallmentNumber=Installment Number
DueDate=Due Date
AmountPrincipal=Principal Amount
AmountInterest=Interest Amount
AmountTotal=Total Amount
PaidAmount=Paid Amount
RemainingAmount=Remaining Amount

# Accounting accounts
AccountCapital=Capital Account
AccountInsurance=Insurance Account
AccountInterest=Interest Account
LoanCapital=Loan Capital Account
LoanInsurance=Loan Insurance Account
LoanInterest=Loan Interest Account
Loans=Loans

# Translations for VIEW methods
LoanSchedule=Loan Schedule
LoanPayment=Loan Payment
LoanRequest=Loan Request
LoanCriteria=Loan Criteria
LoanInstallment=Loan Installment
Scheduled=Scheduled
Unpaid=Unpaid
PartiallyPaid=Partially Paid
Evaluating=Evaluating
ShowSchedule=Show Schedule
ShowPayment=Show Payment
ShowCommittee=Show Committee
ShowCriteria=Show Criteria
ShowInstallment=Show Installment

# Translations for loan requests
DurationMonths=Duration (months)
MonthlySalary=Monthly Salary
OtherLoans=Other Loans
GuarantorName=Guarantor Name
GuarantorPhone=Guarantor Phone
GuarantorAddress=Guarantor Address
DateDecision=Decision Date
RejectionReason=Rejection Reason
EvaluationScore=Evaluation Score
EvaluationComments=Evaluation Comments

# Translations for committee evaluation
LoanRequestCommitteeEvaluation=Loan Request Committee Evaluation
CommitteeEvaluation=Committee Evaluation
EvaluateRequest=Evaluate Request
EvaluationResults=Evaluation Results
Decision=Decision
Approve=Approve
Reject=Reject
SubmitEvaluation=Submit Evaluation
RequestEvaluated=Request evaluated successfully
DateDecision=Decision Date

# Translations for evaluation criteria
EvaluationCriteria=Evaluation Criteria
Weight=Weight
Score=Score
NoCriteriaConfigured=No evaluation criteria configured
ConfigureCriteria=Configure Criteria
CriteriaScores=Criteria Scores

# Translations for evaluation page
LoanRequestEvaluation=Loan Request Evaluation
EvaluationSummary=Evaluation Summary
FinalScore=Final Score
FinalDecision=Final Decision
EvaluationCriteriaDetails=Evaluation Criteria Details
Criteria=Criteria
WeightedScore=Weighted Score
Total=Total
EvaluationHistory=Evaluation History
Action=Action
RequestSubmitted=Request Submitted
RequestApproved=Request Approved
RequestRejected=Request Rejected
NoHistoryAvailable=No history available
RequestNotYetEvaluated=This request has not been evaluated yet

# Translations for committee_decision.php
LoanRequestsToEvaluate=Loan Requests to Evaluate
NoRequestsToEvaluate=No requests to evaluate
Evaluate=Evaluate
EvaluateLoanRequest=Evaluate Loan Request
YouAreNotCommitteeMember=You are not a committee member
BackToList=Back to List
LoanCreatedAutomatically=Loan created automatically following approval

# Translations for actions
RequestCloned=Request cloned successfully
StatusChangedToPending=Status changed to "Pending"
SetToPending=Set to Pending
LoanAndScheduleCreated=Loan and schedule created successfully
ErrorCreatingLoan=Error creating loan
LoanCreatedWithSchedule=Loan created with %s schedules and %s installments
RequestSubmittedForEvaluation=Request submitted for evaluation
ConfirmSubmitForEvaluation=Are you sure you want to submit request %s for evaluation?

# Loan contract
LoanContract=Loan contract
ContractLoanTemplate=Loan contract template
PrintContract=Print contract

# Installment payments
InstallmentPayments=Installment payments
LoanInstallmentPayment=Loan installment payment
InstallmentNumber=Installment number
AmountPrincipal=Principal amount
AmountTotal=Total amount
RecordPayment=Record payment
PaymentRecorded=Payment recorded
NoInstallments=No installments
Pay=Pay
Paid=Paid
Pending=Pending
PaymentSchedule=Payment schedule
NbTerms=Number of terms
Term=Term
Capital=Capital
Insurance=Insurance
Interest=Interest
Total=Total
months=months
LoanCapitalAccount=Loan capital account
AlreadyPaid=Already paid
RemainderToPay=Remainder to pay
DoPayment=Record payment
PaymentMode=Payment mode
BankAccount=Bank account to credit
Numero=Number
NotePrivate=Note (private)
NotePublic=Note (public)