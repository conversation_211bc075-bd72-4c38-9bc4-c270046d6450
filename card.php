<?php

/* Copyright (C) 2014-2018  <PERSON>   <<EMAIL>>
 * Copyright (C) 2015       Frederic France      <<EMAIL>>
 * Copyright (C) 2017       <PERSON>  <<EMAIL>>
 * Copyright (C) 2020       Maxime DEMAREST      <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/**
 *  \file       htdocs/loan/card.php
 *  \ingroup    loan
 *  \brief      Loan card
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/lib/employerloan.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/date.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/functions.lib.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Check permissions
$action = GETPOST('action', 'aZ09');
if ($action == 'create' && !$user->rights->employerloan->write) {
    accessforbidden("Permission denied");
} elseif (!$user->rights->employerloan->read) {
	accessforbidden("Permission denied");
}
if (isModEnabled('accounting')) {
	require_once DOL_DOCUMENT_ROOT.'/core/class/html.formaccounting.class.php';
}
if (isModEnabled('accounting')) {
	require_once DOL_DOCUMENT_ROOT.'/accountancy/class/accountingaccount.class.php';
}
require_once DOL_DOCUMENT_ROOT.'/projet/class/project.class.php';
if (isModEnabled('project')) {
    require_once DOL_DOCUMENT_ROOT.'/core/class/html.formprojet.class.php';
}
require_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';

/**
 * Génère le contenu du contrat de prêt
 */
function generateLoanContract($loan, $employee, $company, $langs)
{
    global $conf;

    // Récupérer la devise de la société
    $currency = $conf->currency;

    $contract = "CONTRAT DE PRÊT ENTRE EMPLOYEUR ET SALARIÉ\n\n";

    $contract .= "Entre les soussignés :\n\n";

    // L'Employeur
    $contract .= "L'Employeur :\n";
    $contract .= "Nom ou Raison sociale : ".$company->name."\n";
    $contract .= "Forme juridique : ".($company->forme_juridique ?: 'SARL')."\n";
    $contract .= "Adresse : ".$company->address."\n";
    $contract .= "         ".$company->zip." ".$company->town."\n";
    $contract .= "SIREN : ".($company->idprof1 ?: 'N/A')."\n";
    $contract .= "Représentée par : ".($company->managers ?: 'Directeur')."\n\n";
    $contract .= "Ci-après dénommé \"le Prêteur\"\n\n";

    $contract .= "et\n\n";

    // Le Salarié
    $contract .= "Le Salarié :\n";
    $contract .= "Nom : ".$employee->lastname."\n";
    $contract .= "Prénom : ".$employee->firstname."\n";
    $contract .= "Date de naissance : ".($employee->birth ? dol_print_date($employee->birth, 'day') : 'N/A')."\n";
    $contract .= "Adresse : ".($employee->address ?: 'N/A')."\n";
    $contract .= "         ".($employee->zip ?: '')." ".($employee->town ?: '')."\n";
    $contract .= "Poste occupé : ".($employee->job ?: 'Employé')."\n";
    $contract .= "Statut : Employé\n\n";
    $contract .= "Ci-après dénommé \"l'Emprunteur\"\n\n";

    $contract .= "Il a été convenu et arrêté ce qui suit :\n\n";

    // Article 1
    $contract .= "Article 1 – Objet du prêt\n";
    $contract .= "Le Prêteur consent à l'Emprunteur un prêt d'un montant de ".price($loan->capital, 0, $langs, 1, -1, -1, $currency)." ";
    $contract .= "destiné à couvrir un besoin ponctuel.\n\n";

    // Article 2
    $contract .= "Article 2 – Conditions de remboursement\n";
    $contract .= "Le remboursement du prêt s'effectuera selon les modalités suivantes :\n\n";
    $contract .= "Durée du prêt : ".$loan->nbterm." mois\n";
    $contract .= "Montant des mensualités : ".price($loan->capital / $loan->nbterm, 0, $langs, 1, -1, -1, $currency)."\n";
    $contract .= "Date de début de remboursement : ".dol_print_date($loan->datestart, 'day')."\n";
    $contract .= "Modalité de paiement : Le prélèvement sera effectué directement sur le salaire de l'emprunteur chaque mois.\n\n";
    $contract .= "Le remboursement sera réalisé sans intérêts.\n\n";

    // Articles suivants...
    $contract .= "Article 3 – Accord écrit préalable\n";
    $contract .= "L'Emprunteur reconnaît avoir pris connaissance des conditions de remboursement et accepte ";
    $contract .= "expressément que ces dernières soient appliquées.\n\n";

    $contract .= "Article 4 – Départ anticipé\n";
    $contract .= "En cas de rupture du contrat de travail avant le terme du remboursement, ";
    $contract .= "l'intégralité du capital restant dû deviendra immédiatement exigible.\n\n";

    $contract .= "Article 5 – Confidentialité\n";
    $contract .= "Les parties s'engagent à garder confidentiel l'existence et les termes du présent contrat.\n\n";

    $contract .= "Article 6 – Clause de non-opposition\n";
    $contract .= "Le présent prêt ne fait pas obstacle à l'exécution normale du contrat de travail.\n\n";

    $contract .= "Article 7 – Droit applicable\n";
    $contract .= "Le présent contrat est régi par le droit français.\n\n";

    // Signatures
    $contract .= "Fait à ".($company->town ?: 'N/A').", le ".dol_print_date(dol_now(), 'day').", en deux exemplaires originaux.\n\n";

    $contract .= "Pour le Prêteur (Employeur)\n";
    $contract .= "Signature : ________________________\n";
    $contract .= "Nom : ".($company->managers ?: 'Directeur')."\n\n";

    $contract .= "Pour l'Emprunteur (Salarié)\n";
    $contract .= "Signature : ________________________\n";
    $contract .= "Nom : ".$employee->getFullName($langs)."\n";

    return $contract;
}

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "compta", "bills", "loan"));

$id = GETPOST('id', 'int');
$action = GETPOST('action', 'aZ09');
$confirm = GETPOST('confirm');
$cancel = GETPOST('cancel', 'alpha');

$projectid = GETPOST('projectid', 'int');

// Security check
$socid = GETPOST('socid', 'int');
if ($user->socid) {
	$socid = $user->socid;
}
$result = restrictedArea($user, 'loan', $id, '', '');

$object = new EmployerLoan($db);

$hookmanager->initHooks(array('loancard', 'globalcard'));

$error = 0;


/*
 * Actions
 */

$parameters = array();
$reshook = $hookmanager->executeHooks('doActions', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks
if ($reshook < 0) {
	setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
}
if (empty($reshook)) {
	// Classify paid
	if ($action == 'confirm_paid' && $confirm == 'yes' && $user->hasRight('loan', 'write')) {
		$object->fetch($id);
		$result = $object->setPaid($user);
		if ($result > 0) {
			setEventMessages($langs->trans('LoanPaid'), null, 'mesgs');
		} else {
			setEventMessages($object->error, $object->errors, 'errors');
		}
	}

	// Delete loan
	if ($action == 'confirm_delete' && $confirm == 'yes' && $user->hasRight('loan', 'write')) {
		$object->fetch($id);
		$result = $object->delete($user);
		if ($result > 0) {
			setEventMessages($langs->trans('LoanDeleted'), null, 'mesgs');
			header("Location: list.php");
			exit;
		} else {
			setEventMessages($object->error, $object->errors, 'errors');
		}
	}

	// Generate contract
	if ($action == 'generate_contract' && $user->hasRight('loan', 'read')) {
		$object->fetch($id);

		// Récupérer les informations de l'employé
		$employee = new User($db);
		$employee->fetch($object->fk_employee);

		// Générer le contrat PDF
		require_once DOL_DOCUMENT_ROOT.'/core/lib/pdf.lib.php';
		require_once DOL_DOCUMENT_ROOT.'/core/lib/company.lib.php';

		// Créer le contenu du contrat
		$contract_content = generateLoanContract($object, $employee, $mysoc, $langs);

		// Générer le PDF
		$pdf = pdf_getInstance();
		$default_font_size = pdf_getPDFFontSize($langs);
		$pdf->SetAutoPageBreak(0); // Désactiver le saut de page automatique
		$pdf->AddPage();
		$pdf->SetFont(pdf_getPDFFont($langs), '', $default_font_size);

		// PAGE 1 : CONTRAT
		// Titre
		$pdf->SetFont(pdf_getPDFFont($langs), 'B', $default_font_size + 6);
		$pdf->Cell(0, 10, $langs->trans('LoanContract'), 0, 1, 'C');
		$pdf->Ln(10);

		// Contenu du contrat
		$pdf->SetFont(pdf_getPDFFont($langs), '', $default_font_size - 1);
		$pdf->MultiCell(0, 4, $contract_content);

		// PAGE 2 : ÉCHÉANCIER
		$pdf->AddPage();

		// Titre échéancier
		$pdf->SetFont(pdf_getPDFFont($langs), 'B', $default_font_size + 4);
		$pdf->Cell(0, 10, $langs->trans('PaymentSchedule'), 0, 1, 'C');
		$pdf->Ln(10);

		// Informations du prêt
		$pdf->SetFont(pdf_getPDFFont($langs), '', $default_font_size);
		$pdf->Cell(50, 8, $langs->trans('Ref').' :', 0, 0);
		$pdf->Cell(0, 8, $object->ref, 0, 1);
		$pdf->Cell(50, 8, $langs->trans('Capital').' :', 0, 0);
		$pdf->Cell(0, 8, price($object->capital, 0, $langs, 1, -1, -1, $conf->currency), 0, 1);
		$pdf->Cell(50, 8, $langs->trans('NbTerms').' :', 0, 0);
		$pdf->Cell(0, 8, $object->nbterm.' '.$langs->trans('months'), 0, 1);
		$pdf->Ln(5);

		// En-tête du tableau d'échéancier
		$pdf->SetFont(pdf_getPDFFont($langs), 'B', $default_font_size - 1);
		$pdf->SetFillColor(220, 220, 220);
		$pdf->Cell(20, 8, $langs->trans('Term'), 1, 0, 'C', 1);
		$pdf->Cell(35, 8, $langs->trans('DueDate'), 1, 0, 'C', 1);
		$pdf->Cell(35, 8, $langs->trans('Capital'), 1, 0, 'C', 1);
		$pdf->Cell(35, 8, $langs->trans('Insurance'), 1, 0, 'C', 1);
		$pdf->Cell(35, 8, $langs->trans('Interest'), 1, 0, 'C', 1);
		$pdf->Cell(35, 8, $langs->trans('Total'), 1, 1, 'C', 1);

		// Récupérer l'échéancier
		$sql_schedule = "SELECT datep, amount_capital, amount_insurance, amount_interest";
		$sql_schedule .= " FROM ".MAIN_DB_PREFIX."employer_loan_schedule";
		$sql_schedule .= " WHERE fk_loan = ".(int) $object->id;
		$sql_schedule .= " ORDER BY datep";

		$resql_schedule = $db->query($sql_schedule);
		if ($resql_schedule) {
			$num_schedule = $db->num_rows($resql_schedule);
			$i = 1;

			$pdf->SetFont(pdf_getPDFFont($langs), '', $default_font_size - 2);

			while ($i <= $num_schedule) {
				$obj_schedule = $db->fetch_object($resql_schedule);

				$total = $obj_schedule->amount_capital + $obj_schedule->amount_insurance + $obj_schedule->amount_interest;

				$pdf->Cell(20, 6, $i, 1, 0, 'C');
				$pdf->Cell(35, 6, dol_print_date($db->jdate($obj_schedule->datep), 'day'), 1, 0, 'C');
				$pdf->Cell(35, 6, price($obj_schedule->amount_capital, 0, $langs, 1, -1, -1, $conf->currency), 1, 0, 'R');
				$pdf->Cell(35, 6, price($obj_schedule->amount_insurance, 0, $langs, 1, -1, -1, $conf->currency), 1, 0, 'R');
				$pdf->Cell(35, 6, price($obj_schedule->amount_interest, 0, $langs, 1, -1, -1, $conf->currency), 1, 0, 'R');
				$pdf->Cell(35, 6, price($total, 0, $langs, 1, -1, -1, $conf->currency), 1, 1, 'R');

				$i++;
			}

			$db->free($resql_schedule);
		}

		// Sortie du PDF
		$filename = 'contrat_pret_'.$object->ref.'.pdf';
		$pdf->Output($filename, 'D');
		exit;
	}

	// Add loan
	if ($action == 'add' && $user->hasRight('loan', 'write')) {
		if (!$cancel) {
			$datestart = dol_mktime(12, 0, 0, GETPOST('startmonth', 'int'), GETPOST('startday', 'int'), GETPOST('startyear', 'int'));
			$dateend = dol_mktime(12, 0, 0, GETPOST('endmonth', 'int'), GETPOST('endday', 'int'), GETPOST('endyear', 'int'));
			$capital = price2num(GETPOST('capital'));
			$rate = price2num(GETPOST('rate'));

			if (!$capital) {
				$error++; $action = 'create';
				setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities("LoanCapital")), null, 'errors');
			}
			if (!$datestart) {
				$error++; $action = 'create';
				setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities("DateStart")), null, 'errors');
			}
			if (!$dateend) {
				$error++; $action = 'create';
				setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities("DateEnd")), null, 'errors');
			}
			if ($rate == '') {
				$error++; $action = 'create';
				setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities("Rate")), null, 'errors');
			}

			if (!$error) {
				$object->label = GETPOST('label');
				$object->fk_bank = GETPOST('accountid');
				$object->capital = $capital;
				$object->datestart = $datestart;
				$object->dateend = $dateend;
				$object->nbterm = GETPOST('nbterm');
				$object->rate = $rate;
				$object->note_private = GETPOST('note_private', 'restricthtml');
				$object->note_public = GETPOST('note_public', 'restricthtml');
				$object->fk_project = GETPOST('projectid', 'int');
				$object->insurance_amount = GETPOST('insurance_amount', 'int');
				$object->fk_employee = GETPOST('fk_employee', 'int');

				$accountancy_account_capital = GETPOST('accountancy_account_capital');
				$accountancy_account_insurance = GETPOST('accountancy_account_insurance');
				$accountancy_account_interest = GETPOST('accountancy_account_interest');

				// Utiliser les rowid spécifiques des comptes comptables
				$object->accountancy_account_capital = '*********';    // Compte comptable capital
				$object->accountancy_account_interest = '*********';   // Compte comptable intérêts
				$object->accountancy_account_insurance = '*********';  // Compte comptable assurance

				$id = $object->create($user);
				if ($id <= 0) {
					$error++;
					setEventMessages($object->error, $object->errors, 'errors');
					$action = 'create';
				} else {
					// L'échéancier et les traites sont créés automatiquement dans la méthode create() de la classe
					setEventMessages($langs->trans("LoanCreatedWithSchedule", $object->nbterm, $object->nbterm), null, 'mesgs');
				}
			}
		} else {
			header("Location: list.php");
			exit();
		}
	} elseif ($action == 'update' && $user->hasRight('loan', 'write')) {
		// Update record
		if (!$cancel) {
			$result = $object->fetch($id);

			$datestart = dol_mktime(12, 0, 0, GETPOST('startmonth', 'int'), GETPOST('startday', 'int'), GETPOST('startyear', 'int'));
			$dateend = dol_mktime(12, 0, 0, GETPOST('endmonth', 'int'), GETPOST('endday', 'int'), GETPOST('endyear', 'int'));
			$capital = price2num(GETPOST('capital'));

			if (!$capital) {
				setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentities("LoanCapital")), null, 'errors');
				$action = 'edit';
			} else {
				$object->datestart = $datestart;
				$object->dateend = $dateend;
				$object->capital = $capital;
				$object->nbterm = GETPOST("nbterm", 'int');
				$object->rate = price2num(GETPOST("rate", 'alpha'));
				$object->insurance_amount = price2num(GETPOST('insurance_amount', 'int'));
				$object->fk_employee = GETPOST('fk_employee', 'int');

				$accountancy_account_capital = GETPOST('accountancy_account_capital');
				$accountancy_account_insurance = GETPOST('accountancy_account_insurance');
				$accountancy_account_interest = GETPOST('accountancy_account_interest');

				// Utiliser les rowid spécifiques des comptes comptables
				$object->accountancy_account_capital = '*********';    // Compte comptable capital
				$object->accountancy_account_interest = '*********';   // Compte comptable intérêts
				$object->accountancy_account_insurance = '*********';  // Compte comptable assurance
			}

			$result = $object->update($user);

			if ($result > 0) {
				header("Location: ".$_SERVER["PHP_SELF"]."?id=".$id);
				exit;
			} else {
				$error++;
				setEventMessages($object->error, $object->errors, 'errors');
			}
		} else {
			header("Location: ".$_SERVER["PHP_SELF"]."?id=".$id);
			exit;
		}
	}

	// Link to a project
	if ($action == 'classin' && $user->hasRight('loan', 'write')) {
		$object->fetch($id);
		$result = $object->setProject($projectid);
		if ($result < 0) {
			setEventMessages($object->error, $object->errors, 'errors');
		}
	}

	if ($action == 'setlabel' && $user->hasRight('loan', 'write')) {
		$object->fetch($id);
		$result = $object->setValueFrom('label', GETPOST('label'), '', '', 'text', '', $user, 'LOAN_MODIFY');
		if ($result < 0) {
			setEventMessages($object->error, $object->errors, 'errors');
		}
	}
}


/*
 * View
 */

$form = new Form($db);
$formproject = null;
if (isModEnabled('project')) {
    if (class_exists('FormProjets')) {
        $formproject = new FormProjets($db);
    } elseif (class_exists('FormProjet')) {
        $formproject = new FormProjet($db);
    }
}
$morehtmlright = '';
$outputlangs = $langs;
if (isModEnabled('accounting')) {
	$formaccounting = new FormAccounting($db);
}

$title = $langs->trans("Loan").' - '.$langs->trans("Card");
$help_url = 'EN:Module_Loan|FR:Module_Emprunt';
llxHeader("", $title, $help_url);


// Create mode
if ($action == 'create') {
	//WYSIWYG Editor
	require_once DOL_DOCUMENT_ROOT.'/core/class/doleditor.class.php';

	print load_fiche_titre($langs->trans("NewLoan"), '', 'money-bill-alt');

	$datec = dol_mktime(12, 0, 0, GETPOST('remonth', 'int'), GETPOST('reday', 'int'), GETPOST('reyear', 'int'));

	print '<form name="loan" action="'.$_SERVER["PHP_SELF"].'" method="POST">'."\n";
	print '<input type="hidden" name="token" value="'.newToken().'">';
	print '<input type="hidden" name="action" value="add">';

	print dol_get_fiche_head();

	print '<table class="border centpercent">';

	// Label
	print '<tr><td class="fieldrequired titlefieldcreate">'.$langs->trans("Label").'</td><td><input name="label" class="minwidth300" maxlength="255" value="'.dol_escape_htmltag(GETPOST('label')).'" autofocus="autofocus"></td></tr>';

	// Bank account
	if (isModEnabled("banque")) {
		print '<tr><td class="fieldrequired">'.$langs->trans("Account").'</td><td>';
		$form->select_comptes(GETPOST("accountid"), "accountid", 0, "courant=1", 1); // Show list of bank account with courant
		print '</td></tr>';
	} else {
		print '<tr><td>'.$langs->trans("Account").'</td><td>';
		print $langs->trans("NoBankAccountDefined");
		print '</td></tr>';
	}

	// Capital
	print '<tr><td class="fieldrequired">'.$langs->trans("LoanCapital").'</td><td><input name="capital" size="10" value="'.dol_escape_htmltag(GETPOST("capital")).'"></td></tr>';

	// Date Start
	print "<tr>";
	print '<td class="fieldrequired">'.$langs->trans("DateStart").'</td><td>';
	print $form->selectDate(!empty($datestart) ? $datestart : -1, 'start', '', '', '', 'add', 1, 1);
	print '</td></tr>';

	// Date End
	print "<tr>";
	print '<td class="fieldrequired">'.$langs->trans("DateEnd").'</td><td>';
	print $form->selectDate(!empty($dateend) ? $dateend : -1, 'end', '', '', '', 'add', 1, 1);
	print '</td></tr>';

	// Number of terms
	print '<tr><td class="fieldrequired">'.$langs->trans("Nbterms").'</td><td><input name="nbterm" size="5" value="'.dol_escape_htmltag(GETPOST('nbterm')).'"></td></tr>';

	// Rate
	print '<tr><td class="fieldrequired">'.$langs->trans("Rate").'</td><td><input name="rate" size="5" value="'.dol_escape_htmltag(GETPOST("rate")).'"> %</td></tr>';

	// Insurance amount
	print '<tr><td>'.$langs->trans("Insurance").'</td><td><input name="insurance_amount" size="10" value="'.dol_escape_htmltag(GETPOST("insurance_amount")).'" placeholder="'.$langs->trans('Amount').'"></td></tr>';

	// Employee
	print '<tr>';
	print '<td>'.$langs->trans("Employee").'</td>';
	print '<td>';
	print $form->select_dolusers(0, 'fk_employee', 1, '', 0, '', '', 0, 0, 0, '', 0, '', 'user', 0, 0, '', 1);
	print '</td>';
	print '</tr>';

	// Project
	if (isModEnabled('project') && $formproject) {
		// Projet associe
		$langs->loadLangs(array("projects"));

		print '<tr><td>'.$langs->trans("Project").'</td><td>';

		$numproject = $formproject->select_projects(-1, $projectid, 'projectid', 16, 0, 1, 1);

		print '</td></tr>';
	}

	// Note Private
	print '<tr>';
	print '<td class="tdtop">'.$langs->trans('NotePrivate').'</td>';
	print '<td>';

	$doleditor = new DolEditor('note_private', GETPOST('note_private', 'alpha'), '', 160, 'dolibarr_notes', 'In', false, true, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PUBLIC) ? 0 : 1, ROWS_6, '90%');
	print $doleditor->Create(1);

	print '</td></tr>';

	// Note Public
	print '<tr>';
	print '<td class="tdtop">'.$langs->trans('NotePublic').'</td>';
	print '<td>';
	$doleditor = new DolEditor('note_public', GETPOST('note_public', 'alpha'), '', 160, 'dolibarr_notes', 'In', false, true, empty($conf->global->FCKEDITOR_ENABLE_NOTE_PRIVATE) ? 0 : 1, ROWS_6, '90%');
	print $doleditor->Create(1);
	print '</td></tr>';

	// Accountancy
	if (isModEnabled('accounting')) {
		// Accountancy_account_capital
		print '<tr><td class="titlefieldcreate fieldrequired">'.$langs->trans("LoanAccountancyCapitalCode").'</td>';
		print '<td>';
		print $formaccounting->select_account(GETPOST('accountancy_account_capital') ?GETPOST('accountancy_account_capital') : getDolGlobalString('EMPLOYERLOAN_ACCOUNTING_ACCOUNT_CAPITAL'), 'accountancy_account_capital', 1, '', 1, 1);
		print '</td></tr>';

		// Accountancy_account_insurance
		print '<tr><td class="fieldrequired">'.$langs->trans("LoanAccountancyInsuranceCode").'</td>';
		print '<td>';
		print $formaccounting->select_account(GETPOST('accountancy_account_insurance') ?GETPOST('accountancy_account_insurance') : getDolGlobalString('EMPLOYERLOAN_ACCOUNTING_ACCOUNT_INSURANCE'), 'accountancy_account_insurance', 1, '', 1, 1);
		print '</td></tr>';

		// Accountancy_account_interest
		print '<tr><td class="fieldrequired">'.$langs->trans("LoanAccountancyInterestCode").'</td>';
		print '<td>';
		print $formaccounting->select_account(GETPOST('accountancy_account_interest') ?GETPOST('accountancy_account_interest') : getDolGlobalString('EMPLOYERLOAN_ACCOUNTING_ACCOUNT_INTEREST'), 'accountancy_account_interest', 1, '', 1, 1);
		print '</td></tr>';
	} else {
		// For external software
		// Accountancy_account_capital
		print '<tr><td class="titlefieldcreate">'.$langs->trans("LoanAccountancyCapitalCode").'</td>';
		print '<td><input name="accountancy_account_capital" size="16" value="'.$object->accountancy_account_capital.'">';
		print '</td></tr>';

		// Accountancy_account_insurance
		print '<tr><td>'.$langs->trans("LoanAccountancyInsuranceCode").'</td>';
		print '<td><input name="accountancy_account_insurance" size="16" value="'.$object->accountancy_account_insurance.'">';
		print '</td></tr>';

		// Accountancy_account_interest
		print '<tr><td>'.$langs->trans("LoanAccountancyInterestCode").'</td>';
		print '<td><input name="accountancy_account_interest" size="16" value="'.$object->accountancy_account_interest.'">';
		print '</td></tr>';
	}
	print '</table>';

	print dol_get_fiche_end();

	print $form->buttonsSaveCancel("Add");

	print '</form>';
}

// View
if ($id > 0) {
	$object = new EmployerLoan($db);
	$result = $object->fetch($id);

	if ($result > 0) {
		$head = loan_prepare_head($object);

		$totalpaid = $object->getSumPayment();

		// Confirm for loan
		if ($action == 'paid') {
			$text = $langs->trans('ConfirmPayLoan');
			print $form->formconfirm($_SERVER["PHP_SELF"]."?id=".$object->id, $langs->trans('PayLoan'), $text, "confirm_paid", '', '', 2);
		}

		if ($action == 'delete') {
			$text = $langs->trans('ConfirmDeleteLoan');
			print $form->formconfirm($_SERVER['PHP_SELF'].'?id='.$object->id, $langs->trans('DeleteLoan'), $text, 'confirm_delete', '', '', 2);
		}

		if ($action == 'edit') {
			print '<form name="update" action="'.$_SERVER["PHP_SELF"].'" method="POST">'."\n";
			print '<input type="hidden" name="token" value="'.newToken().'">';
			print '<input type="hidden" name="action" value="update">';
			print '<input type="hidden" name="id" value="'.$id.'">';
		}

		print dol_get_fiche_head($head, 'card', $langs->trans("Loan"), -1, 'bill');

		// Loan card

		$linkback = '<a href="'.DOL_URL_ROOT.'/custom/employerloan/list.php?restore_lastsearch_values=1">'.$langs->trans("BackToList").'</a>';

		$morehtmlref = '<div class="refidno">';
		// Ref loan
		$morehtmlref .= $form->editfieldkey("Label", 'label', $object->label, $object, $user->hasRight('loan', 'write'), 'string', '', 0, 1);
		$morehtmlref .= $form->editfieldval("Label", 'label', $object->label, $object, $user->hasRight('loan', 'write'), 'string', '', null, null, '', 1);
		// Project
		if (isModEnabled('project')) {
			$langs->loadLangs(array("projects"));
			$morehtmlref .= '<br>'.$langs->trans('Project').' ';
			if ($user->hasRight('loan', 'write')) {
				if ($action != 'classify') {
					$morehtmlref .= '<a class="editfielda" href="'.$_SERVER['PHP_SELF'].'?action=classify&token='.newToken().'&id='.$object->id.'">'.img_edit($langs->transnoentitiesnoconv('SetProject')).'</a> : ';
				}
				if ($action == 'classify' && $formproject) {
					//$morehtmlref.=$form->form_project($_SERVER['PHP_SELF'] . '?id=' . $object->id, $object->socid, $object->fk_project, 'projectid', 0, 0, 1, 1);
					$morehtmlref .= '<form method="post" action="'.$_SERVER['PHP_SELF'].'?id='.$object->id.'">';
					$morehtmlref .= '<input type="hidden" name="action" value="classin">';
					$morehtmlref .= '<input type="hidden" name="token" value="'.newToken().'">';
					$morehtmlref .= $formproject->select_projects(-1, $object->fk_project, 'projectid', $maxlength, 0, 1, 0, 1, 0, 0, '', 1);
					$morehtmlref .= '<input type="submit" class="button valignmiddle" value="'.$langs->trans("Modify").'">';
					$morehtmlref .= '</form>';
				} else {
					$morehtmlref .= $form->form_project($_SERVER['PHP_SELF'].'?id='.$object->id, -1, $object->fk_project, 'none', 0, 0, 0, 1, '', 'maxwidth300');
				}
			} else {
				if (!empty($object->fk_project)) {
					$proj = new Project($db);
					$proj->fetch($object->fk_project);
					$morehtmlref .= ' : '.$proj->getNomUrl(1);
					if ($proj->title) {
						$morehtmlref .= ' - '.$proj->title;
					}
				} else {
					$morehtmlref .= '';
				}
			}
		}
		$morehtmlref .= '</div>';

		$object->totalpaid = $totalpaid; // To give a chance to dol_banner_tab to use already paid amount to show correct status

		dol_banner_tab($object, 'id', $linkback, 1, 'rowid', 'ref', $morehtmlref, '', 0, '', $morehtmlright);

		print '<div class="fichecenter">';
		print '<div class="fichehalfleft">';
		print '<div class="underbanner clearboth"></div>';

		print '<table class="border centpercent tableforfield">';

		// Capital
		if ($action == 'edit') {
			print '<tr><td class="fieldrequired titlefield">'.$langs->trans("LoanCapital").'</td><td>';
			print '<input name="capital" size="10" value="'.$object->capital.'"></td></tr>';
			print '</td></tr>';
		} else {
			print '<tr><td class="titlefield">'.$langs->trans("LoanCapital").'</td><td><span class="amount">'.price($object->capital, 0, $outputlangs, 1, -1, -1, $conf->currency).'</span></td></tr>';
		}

		// Insurance
		if ($action == 'edit') {
			print '<tr><td class="titlefield">'.$langs->trans("Insurance").'</td><td>';
			print '<input name="insurance_amount" size="10" value="'.$object->insurance_amount.'"></td></tr>';
			print '</td></tr>';
		} else {
			print '<tr><td class="titlefield">'.$langs->trans("Insurance").'</td><td><span class="amount">'.price($object->insurance_amount, 0, $outputlangs, 1, -1, -1, $conf->currency).'</span></td></tr>';
		}

		// Date start
		print '<tr><td>'.$langs->trans("DateStart")."</td>";
		print "<td>";
		if ($action == 'edit') {
			print $form->selectDate($object->datestart, 'start', 0, 0, 0, 'update', 1, 0);
		} else {
			print dol_print_date($object->datestart, "day");
		}
		print "</td></tr>";

		// Date end
		print '<tr><td>'.$langs->trans("DateEnd")."</td>";
		print "<td>";
		if ($action == 'edit') {
			print $form->selectDate($object->dateend, 'end', 0, 0, 0, 'update', 1, 0);
		} else {
			print dol_print_date($object->dateend, "day");
		}
		print "</td></tr>";

		// Nbterms
		print '<tr><td>'.$langs->trans("Nbterms").'</td>';
		print '<td>';
		if ($action == 'edit') {
			print '<input name="nbterm" size="4" value="'.$object->nbterm.'">';
		} else {
			print $object->nbterm;
		}
		print '</td></tr>';

		// Rate
		print '<tr><td>'.$langs->trans("Rate").'</td>';
		print '<td>';
		if ($action == 'edit') {
			print '<input name="rate" size="4" value="'.$object->rate.'">%';
		} else {
			print price($object->rate).'%';
		}
		print '</td></tr>';

		// Accountancy account capital
		print '<tr>';
		if ($action == 'edit') {
			print '<td class="nowrap fieldrequired">';
			print $langs->trans("LoanAccountancyCapitalCode");
			print '</td><td>';

			if (isModEnabled('accounting')) {
				print $formaccounting->select_account($object->accountancy_account_capital, 'accountancy_account_capital', 1, '', 1, 1);
			} else {
				print '<input name="accountancy_account_capital" size="16" value="'.$object->accountancy_account_capital.'">';
			}
			print '</td>';
		} else {
			print '<td class="nowrap">';
			print $langs->trans("LoanAccountancyCapitalCode");
			print '</td><td>';

			if (isModEnabled('accounting')) {
				$accountingaccount = new AccountingAccount($db);
				$accountingaccount->fetch($object->accountancy_account_capital);

				print $accountingaccount->getNomUrl(0, 1, 1, '', 1);
			} else {
				// Récupérer le code et libellé depuis la table accounting_account avec le rowid
				$sql_account = "SELECT account_number, label FROM ".MAIN_DB_PREFIX."accounting_account WHERE rowid = ".(int)$object->accountancy_account_capital;
				$resql_account = $db->query($sql_account);
				if ($resql_account && $db->num_rows($resql_account) > 0) {
					$obj_account = $db->fetch_object($resql_account);
					print $obj_account->account_number.' - '.$obj_account->label;
				} else {
					print $object->accountancy_account_capital;
				}
			}

			print '</td>';
		}
		print '</tr>';

		// Accountancy account insurance
		print '<tr>';
		if ($action == 'edit') {
			print '<td class="nowrap fieldrequired">';
			print $langs->trans("LoanAccountancyInsuranceCode");
			print '</td><td>';

			if (isModEnabled('accounting')) {
				print $formaccounting->select_account($object->accountancy_account_insurance, 'accountancy_account_insurance', 1, '', 1, 1);
			} else {
				print '<input name="accountancy_account_insurance" size="16" value="'.$object->accountancy_account_insurance.'">';
			}
			print '</td>';
		} else {
			print '<td class="nowrap">';
			print $langs->trans("LoanAccountancyInsuranceCode");
			print '</td><td>';

			if (isModEnabled('accounting')) {
				$accountingaccount = new AccountingAccount($db);
				$accountingaccount->fetch($object->accountancy_account_insurance);

				print $accountingaccount->getNomUrl(0, 1, 1, '', 1);
			} else {
				// Récupérer le code et libellé depuis la table accounting_account avec le rowid
				$sql_account = "SELECT account_number, label FROM ".MAIN_DB_PREFIX."accounting_account WHERE rowid = ".(int)$object->accountancy_account_insurance;
				$resql_account = $db->query($sql_account);
				if ($resql_account && $db->num_rows($resql_account) > 0) {
					$obj_account = $db->fetch_object($resql_account);
					print $obj_account->account_number.' - '.$obj_account->label;
				} else {
					print $object->accountancy_account_insurance;
				}
			}

			print '</td>';
		}
		print '</tr>';

		// Accountancy account interest
		print '<tr>';
		if ($action == 'edit') {
			print '<td class="nowrap fieldrequired">';
			print $langs->trans("LoanAccountancyInterestCode");
			print '</td><td>';

			if (isModEnabled('accounting')) {
				print $formaccounting->select_account($object->accountancy_account_interest, 'accountancy_account_interest', 1, '', 1, 1);
			} else {
				print '<input name="accountancy_account_interest" size="16" value="'.$object->accountancy_account_interest.'">';
			}
			print '</td>';
		} else {
			print '<td class="nowrap">';
			print $langs->trans("LoanAccountancyInterestCode");
			print '</td><td>';

			if (isModEnabled('accounting')) {
				$accountingaccount = new AccountingAccount($db);
				$accountingaccount->fetch($object->accountancy_account_interest);

				print $accountingaccount->getNomUrl(0, 1, 1, '', 1);
			} else {
				// Récupérer le code et libellé depuis la table accounting_account avec le rowid
				$sql_account = "SELECT account_number, label FROM ".MAIN_DB_PREFIX."accounting_account WHERE rowid = ".(int)$object->accountancy_account_interest;
				$resql_account = $db->query($sql_account);
				if ($resql_account && $db->num_rows($resql_account) > 0) {
					$obj_account = $db->fetch_object($resql_account);
					print $obj_account->account_number.' - '.$obj_account->label;
				} else {
					print $object->accountancy_account_interest;
				}
			}

			print '</td>';
		}
		print '</tr>';

		// Other attributes
		$parameters = array();
		$reshook = $hookmanager->executeHooks('formObjectOptions', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
		print $hookmanager->resPrint;

		print '</table>';

		print '</div>';
		print '<div class="fichehalfright">';

		/*
		 * Payments
		 */
		$sql = "SELECT p.rowid, p.num_payment, p.datep as dp,";
		$sql .= " p.amount_capital, p.amount_insurance, p.amount_interest,";
		$sql .= " b.fk_account,";
		$sql .= " c.libelle as paiement_type";
		$sql .= " FROM ".MAIN_DB_PREFIX."employer_payment_loan as p";
		$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."bank as b ON p.fk_bank = b.rowid";
		$sql .= " LEFT JOIN ".MAIN_DB_PREFIX."c_paiement as c ON p.fk_typepayment = c.id,";
		$sql .= " ".MAIN_DB_PREFIX."employer_loan as l";
		$sql .= " WHERE p.fk_loan = ".((int) $id);
		$sql .= " AND p.fk_loan = l.rowid";
		$sql .= " AND l.entity IN ( ".getEntity('loan').")";
		$sql .= " ORDER BY dp DESC";

		//print $sql;
		$resql = $db->query($sql);
		if ($resql) {
			$num = $db->num_rows($resql);
			$i = 0;
			$total_insurance = 0;
			$total_interest = 0;
			$total_capital = 0;

			print '<div class="div-table-responsive-no-min">'; // You can use div-table-responsive-no-min if you dont need reserved height for your table
			print '<table class="noborder paymenttable">';
			print '<tr class="liste_titre">';
			print '<td>'.$langs->trans("RefPayment").'</td>';
			print '<td>'.$langs->trans("Date").'</td>';
			print '<td>'.$langs->trans("Type").'</td>';
			print '<td>'.$langs->trans("BankAccount").'</td>';
			print '<td class="right">'.$langs->trans("Insurance").'</td>';
			print '<td class="right">'.$langs->trans("Interest").'</td>';
			print '<td class="right">'.$langs->trans("LoanCapital").'</td>';
			print '</tr>';

			$conf->cache['bankaccount'] = array();

			while ($i < $num) {
				$objp = $db->fetch_object($resql);

				print '<tr class="oddeven">';
				print '<td><a href="'.DOL_URL_ROOT.'/custom/employerloan/payment/card.php?id='.$objp->rowid.'">'.img_object($langs->trans("Payment"), "payment").' '.$objp->rowid.'</a></td>';
				print '<td>'.dol_print_date($db->jdate($objp->dp), 'day')."</td>\n";
				print "<td>".$objp->paiement_type.' '.$objp->num_payment."</td>\n";
				print "<td>";
				if (!empty($conf->cache['bankaccount'][$objp->fk_account])) {
					$tmpbank = $conf->cache['bankaccount'][$objp->fk_account];
				} else {
					$tmpbank = new Account($db);
					$tmpbank->fetch($objp->fk_account);
					$conf->cache['bankaccount'][$objp->fk_account] = $tmpbank;
				}
				print $tmpbank->getNomUrl(1);
				print "</td>\n";
				print '<td class="nowrap right"><span class="amount">'.price($objp->amount_insurance, 0, $outputlangs, 1, -1, -1, $conf->currency)."</span></td>\n";
				print '<td class="nowrap right"><span class="amount">'.price($objp->amount_interest, 0, $outputlangs, 1, -1, -1, $conf->currency)."</span></td>\n";
				print '<td class="nowrap right"><span class="amount">'.price($objp->amount_capital, 0, $outputlangs, 1, -1, -1, $conf->currency)."</span></td>\n";
				print "</tr>";
				$total_capital += $objp->amount_capital;
				$i++;
			}

			$totalpaid = $total_capital;

			if ($object->paid == 0 || $object->paid == 2) {
				print '<tr><td colspan="6" class="right">'.$langs->trans("AlreadyPaid").' :</td><td class="nowrap right">'.price($totalpaid, 0, $langs, 0, -1, -1, $conf->currency).'</td></tr>';
				print '<tr><td colspan="6" class="right">'.$langs->trans("AmountExpected").' :</td><td class="nowrap right">'.price($object->capital, 0, $outputlangs, 1, -1, -1, $conf->currency).'</td></tr>';

				$staytopay = $object->capital - $totalpaid;

				print '<tr><td colspan="6" class="right">'.$langs->trans("RemainderToPay").' :</td>';
				print '<td class="nowrap right'.($staytopay ? ' amountremaintopay' : ' amountpaymentcomplete').'">';
				print price($staytopay, 0, $langs, 0, -1, -1, $conf->currency);
				print '</td></tr>';
			}
			print "</table>";
			print '</div>';

			$db->free($resql);
		} else {
			dol_print_error($db);
		}

		print '</div>';
		print '</div>';

		print '<div class="clearboth"></div>';

		print dol_get_fiche_end();

		if ($action == 'edit') {
			print $form->buttonsSaveCancel();

			print '</form>';
		}

		/*
		 *  Buttons actions
		 */
		if ($action != 'edit') {
			$reshook = $hookmanager->executeHooks('addMoreActionsButtons', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
			if (empty($reshook)) {
				print '<div class="tabsAction">';

				// Edit
				if (($object->paid == 0 || $object->paid == 2) && $user->hasRight('loan', 'write')) {
					print '<div class="inline-block divButAction"><a class="butAction" href="'.DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id.'&action=edit&token='.newToken().'">'.$langs->trans("Modify").'</a></div>';
				}

				// Generate contract
				if ($object->id > 0 && $user->hasRight('loan', 'read')) {
					print '<div class="inline-block divButAction"><a class="butAction" href="'.DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id.'&action=generate_contract&token='.newToken().'">'.$langs->trans("PrintContract").'</a></div>';
				}

				// Emit payment
				if (($object->paid == 0 || $object->paid == 2) && ((price2num($object->capital) > 0 && round($staytopay) < 0) || (price2num($object->capital) > 0 && round($staytopay) > 0)) && $user->hasRight('loan', 'write')) {
					print '<div class="inline-block divButAction"><a class="butAction" href="'.DOL_URL_ROOT.'/custom/employerloan/payment/payment.php?id='.$object->id.'&action=create&token='.newToken().'">'.$langs->trans("DoPayment").'</a></div>';
				}

				// Classify 'paid'
				if (($object->paid == 0 || $object->paid == 2) && round($staytopay) <= 0 && $user->hasRight('loan', 'write')) {
					print '<div class="inline-block divButAction"><a class="butAction" href="'.DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id.'&action=paid&token='.newToken().'">'.$langs->trans("ClassifyPaid").'</a></div>';
				}

				// Delete
				if (($object->paid == 0 || $object->paid == 2) && $user->rights->loan->delete) {
					print '<div class="inline-block divButAction"><a class="butActionDelete" href="'.DOL_URL_ROOT.'/custom/employerloan/card.php?id='.$object->id.'&action=delete&token='.newToken().'">'.$langs->trans("Delete").'</a></div>';
				}

				print "</div>";
			}
		}
	} else {
		// Loan not found
		dol_print_error('', $object->error);
	}
}

// End of page
llxFooter();
$db->close();
