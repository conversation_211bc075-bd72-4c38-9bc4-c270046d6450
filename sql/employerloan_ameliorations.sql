-- Copyright (C) 2024 NextGestion
--
-- This program is free software: you can redistribute it and/or modify
-- it under the terms of the GNU General Public License as published by
-- the Free Software Foundation, either version 3 of the License, or
-- (at your option) any later version.
--
-- This program is distributed in the hope that it will be useful,
-- but WITHOUT ANY WARRANTY; without even the implied warranty of
-- MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
-- GNU General Public License for more details.
--
-- You should have received a copy of the GNU General Public License
-- along with this program.  If not, see <http://www.gnu.org/licenses/>.

-- ============================================================================
-- Tables pour les améliorations du module employerloan
-- ============================================================================

-- Table pour les demandes de crédit
CREATE TABLE IF NOT EXISTS llx_employer_loan_request (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    entity int(11) NOT NULL DEFAULT 1,
    ref varchar(30) NOT NULL,
    fk_employee int(11) NOT NULL,
    amount_requested decimal(24,8) NOT NULL,
    purpose text NOT NULL,
    duration_months int(11) NOT NULL,
    monthly_salary decimal(24,8) DEFAULT NULL,
    other_loans decimal(24,8) DEFAULT 0.********,
    guarantor_name varchar(255) DEFAULT NULL,
    guarantor_phone varchar(20) DEFAULT NULL,
    guarantor_address text DEFAULT NULL,
    status int(11) NOT NULL DEFAULT 0 COMMENT '0=En attente, 1=En cours évaluation, 2=Approuvé, 3=Rejeté, 4=Annulé',
    date_request datetime NOT NULL,
    date_evaluation datetime DEFAULT NULL,
    date_decision datetime DEFAULT NULL,
    fk_user_evaluator int(11) DEFAULT NULL,
    evaluation_score decimal(5,2) DEFAULT NULL,
    evaluation_comments text DEFAULT NULL,
    rejection_reason text DEFAULT NULL,
    note_private text DEFAULT NULL,
    note_public text DEFAULT NULL,
    date_creation datetime NOT NULL,
    fk_user_creat int(11) NOT NULL,
    date_modification datetime DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    UNIQUE KEY uk_ref (ref, entity),
    KEY idx_fk_employee (fk_employee),
    KEY idx_status (status),
    KEY idx_entity (entity),
    CONSTRAINT fk_loan_request_employee FOREIGN KEY (fk_employee) REFERENCES llx_user(rowid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour les critères d'évaluation des prêts
CREATE TABLE IF NOT EXISTS llx_employer_loan_criteria (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    entity int(11) NOT NULL DEFAULT 1,
    code varchar(50) NOT NULL,
    label varchar(255) NOT NULL,
    description text DEFAULT NULL,
    weight decimal(5,2) NOT NULL DEFAULT 1.00,
    min_value decimal(10,2) DEFAULT NULL,
    max_value decimal(10,2) DEFAULT NULL,
    formula text DEFAULT NULL,
    active tinyint(1) NOT NULL DEFAULT 1,
    date_creation datetime NOT NULL,
    fk_user_creat int(11) NOT NULL,
    date_modification datetime DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    UNIQUE KEY uk_code_entity (code, entity),
    KEY idx_entity (entity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour l'évaluation détaillée des demandes
CREATE TABLE IF NOT EXISTS llx_employer_loan_evaluation (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_loan_request int(11) NOT NULL,
    fk_criteria int(11) NOT NULL,
    value_obtained decimal(10,2) NOT NULL,
    score decimal(5,2) NOT NULL,
    comments text DEFAULT NULL,
    date_evaluation datetime NOT NULL,
    fk_user_evaluator int(11) NOT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_loan_request (fk_loan_request),
    KEY idx_fk_criteria (fk_criteria),
    CONSTRAINT fk_evaluation_request FOREIGN KEY (fk_loan_request) REFERENCES llx_employer_loan_request(rowid) ON DELETE CASCADE,
    CONSTRAINT fk_evaluation_criteria FOREIGN KEY (fk_criteria) REFERENCES llx_employer_loan_criteria(rowid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour les membres du comité d'approbation
CREATE TABLE IF NOT EXISTS llx_employer_loan_committee (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    entity int(11) NOT NULL DEFAULT 1,
    fk_user int(11) NOT NULL,
    role varchar(50) NOT NULL COMMENT 'president, member, secretary',
    max_amount decimal(24,8) DEFAULT NULL COMMENT 'Montant maximum qu\'il peut approuver seul',
    active tinyint(1) NOT NULL DEFAULT 1,
    date_creation datetime NOT NULL,
    fk_user_creat int(11) NOT NULL,
    date_modification datetime DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_user (fk_user),
    KEY idx_entity (entity),
    CONSTRAINT fk_committee_user FOREIGN KEY (fk_user) REFERENCES llx_user(rowid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour les décisions du comité
CREATE TABLE IF NOT EXISTS llx_employer_loan_committee_decision (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_loan_request int(11) NOT NULL,
    fk_committee_member int(11) NOT NULL,
    decision varchar(20) NOT NULL COMMENT 'approve, reject, abstain',
    comments text DEFAULT NULL,
    date_decision datetime NOT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_loan_request (fk_loan_request),
    KEY idx_fk_committee_member (fk_committee_member),
    CONSTRAINT fk_decision_request FOREIGN KEY (fk_loan_request) REFERENCES llx_employer_loan_request(rowid) ON DELETE CASCADE,
    CONSTRAINT fk_decision_committee FOREIGN KEY (fk_committee_member) REFERENCES llx_employer_loan_committee(rowid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour les traites générées
CREATE TABLE IF NOT EXISTS llx_employer_loan_installment (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_loan int(11) NOT NULL,
    installment_number int(11) NOT NULL,
    due_date date NOT NULL,
    amount_principal decimal(24,8) NOT NULL,
    amount_interest decimal(24,8) NOT NULL,
    amount_total decimal(24,8) NOT NULL,
    status varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'pending, paid, overdue, cancelled',
    date_payment datetime DEFAULT NULL,
    amount_paid decimal(24,8) DEFAULT 0.********,
    payment_method varchar(50) DEFAULT 'cash' COMMENT 'cash, bank_transfer, check',
    fk_payment int(11) DEFAULT NULL,
    note_private text DEFAULT NULL,
    note_public text DEFAULT NULL,
    date_creation datetime NOT NULL,
    fk_user_creat int(11) NOT NULL,
    date_modification datetime DEFAULT NULL,
    fk_user_modif int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_loan (fk_loan),
    KEY idx_due_date (due_date),
    KEY idx_status (status),
    CONSTRAINT fk_installment_loan FOREIGN KEY (fk_loan) REFERENCES llx_employer_loan(rowid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour l'historique des prêts par salarié
CREATE TABLE IF NOT EXISTS llx_employer_loan_history (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_employee int(11) NOT NULL,
    fk_loan int(11) DEFAULT NULL,
    fk_loan_request int(11) DEFAULT NULL,
    event_type varchar(50) NOT NULL COMMENT 'request_created, request_approved, request_rejected, loan_created, payment_made, loan_completed',
    event_date datetime NOT NULL,
    amount decimal(24,8) DEFAULT NULL,
    description text DEFAULT NULL,
    fk_user_action int(11) DEFAULT NULL,
    PRIMARY KEY (rowid),
    KEY idx_fk_employee (fk_employee),
    KEY idx_fk_loan (fk_loan),
    KEY idx_event_type (event_type),
    KEY idx_event_date (event_date),
    CONSTRAINT fk_history_employee FOREIGN KEY (fk_employee) REFERENCES llx_user(rowid) ON DELETE CASCADE,
    CONSTRAINT fk_history_loan FOREIGN KEY (fk_loan) REFERENCES llx_employer_loan(rowid) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table pour les documents générés (contrats, traites, etc.)
CREATE TABLE IF NOT EXISTS llx_employer_loan_document (
    rowid int(11) NOT NULL AUTO_INCREMENT,
    fk_loan int(11) DEFAULT NULL,
    fk_loan_request int(11) DEFAULT NULL,
    document_type varchar(50) NOT NULL COMMENT 'contract, installment_schedule, payment_receipt, guarantee',
    document_name varchar(255) NOT NULL,
    document_path varchar(500) NOT NULL,
    file_size int(11) DEFAULT NULL,
    mime_type varchar(100) DEFAULT NULL,
    date_generation datetime NOT NULL,
    fk_user_generation int(11) NOT NULL,
    active tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY (rowid),
    KEY idx_fk_loan (fk_loan),
    KEY idx_fk_loan_request (fk_loan_request),
    KEY idx_document_type (document_type),
    CONSTRAINT fk_document_loan FOREIGN KEY (fk_loan) REFERENCES llx_employer_loan(rowid) ON DELETE CASCADE,
    CONSTRAINT fk_document_request FOREIGN KEY (fk_loan_request) REFERENCES llx_employer_loan_request(rowid) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Ajout de colonnes à la table principale des prêts
ALTER TABLE llx_employer_loan 
ADD COLUMN IF NOT EXISTS fk_loan_request int(11) DEFAULT NULL COMMENT 'Lien vers la demande de crédit',
ADD COLUMN IF NOT EXISTS payment_method varchar(50) DEFAULT 'cash' COMMENT 'Méthode de paiement par défaut',
ADD COLUMN IF NOT EXISTS contract_signed tinyint(1) DEFAULT 0 COMMENT 'Contrat signé',
ADD COLUMN IF NOT EXISTS contract_date datetime DEFAULT NULL COMMENT 'Date signature contrat',
ADD COLUMN IF NOT EXISTS guarantor_name varchar(255) DEFAULT NULL COMMENT 'Nom du garant',
ADD COLUMN IF NOT EXISTS guarantor_phone varchar(20) DEFAULT NULL COMMENT 'Téléphone du garant',
ADD COLUMN IF NOT EXISTS guarantor_address text DEFAULT NULL COMMENT 'Adresse du garant',
ADD COLUMN IF NOT EXISTS status varchar(20) DEFAULT 'active' COMMENT 'active, completed, cancelled, defaulted';

-- Contrainte pour lier le prêt à la demande
ALTER TABLE llx_employer_loan 
ADD CONSTRAINT IF NOT EXISTS fk_loan_request FOREIGN KEY (fk_loan_request) REFERENCES llx_employer_loan_request(rowid) ON DELETE SET NULL;

-- Insertion des critères par défaut
INSERT IGNORE INTO llx_employer_loan_criteria (entity, code, label, description, weight, min_value, max_value, date_creation, fk_user_creat) VALUES
(1, 'SALARY_RATIO', 'Ratio salaire/prêt', 'Le montant du prêt ne doit pas dépasser 10 fois le salaire mensuel', 3.00, 0, 10, NOW(), 1),
(1, 'SENIORITY', 'Ancienneté', 'Ancienneté minimum de 6 mois dans l\'entreprise', 2.00, 6, NULL, NOW(), 1),
(1, 'EXISTING_LOANS', 'Prêts existants', 'Montant total des prêts en cours', 2.50, 0, 50000, NOW(), 1),
(1, 'PAYMENT_HISTORY', 'Historique paiements', 'Historique des paiements précédents', 2.00, 0, 100, NOW(), 1),
(1, 'GUARANTOR', 'Garant', 'Présence d\'un garant solvable', 1.50, 0, 1, NOW(), 1),
(1, 'PURPOSE', 'Objet du prêt', 'Légitimité de l\'objet du prêt', 1.00, 0, 100, NOW(), 1);
