<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * \file    committee_setup.php
 * \ingroup employerloan
 * \brief   Setup page for loan committee
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/user/class/user.class.php';
dol_include_once('/employerloan/class/employerloan_committee.class.php');

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "admin", "users"));

// Get parameters
$action = GETPOST('action', 'aZ09');
$id = GETPOST('id', 'int');
$confirm = GETPOST('confirm', 'alpha');

// Security check
if (!$user->admin) {
    accessforbidden();
}

// Initialize technical objects
$object = new EmployerLoanCommittee($db);

/*
 * Actions
 */

if ($action == 'add' && GETPOST('cancel', 'alpha') == '') {
    $error = 0;
    
    $object->fk_user = GETPOST('fk_user', 'int');
    $object->role = GETPOST('role', 'alpha');
    $object->max_amount = GETPOST('max_amount', 'numeric');
    $object->active = GETPOST('active', 'int');
    $object->entity = $conf->entity;
    $object->date_creation = dol_now();
    $object->fk_user_creat = $user->id;
    
    if (empty($object->fk_user)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("User")), null, 'errors');
        $error++;
    }
    if (empty($object->role)) {
        setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Role")), null, 'errors');
        $error++;
    }
    
    if (!$error) {
        $result = $object->create($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberAdded"), null, 'mesgs');
            $action = '';
        } else {
            setEventMessages($object->error, $object->errors, 'errors');
            $action = 'create';
        }
    } else {
        $action = 'create';
    }
}

if ($action == 'update' && GETPOST('cancel', 'alpha') == '') {
    $error = 0;
    
    if ($object->fetch($id) > 0) {
        $object->fk_user = GETPOST('fk_user', 'int');
        $object->role = GETPOST('role', 'alpha');
        $object->max_amount = GETPOST('max_amount', 'numeric');
        $object->active = GETPOST('active', 'int');
        
        if (empty($object->fk_user)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("User")), null, 'errors');
            $error++;
        }
        if (empty($object->role)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Role")), null, 'errors');
            $error++;
        }
        
        if (!$error) {
            $result = $object->update($user);
            if ($result > 0) {
                setEventMessages($langs->trans("CommitteeMemberUpdated"), null, 'mesgs');
                $action = '';
            } else {
                setEventMessages($object->error, $object->errors, 'errors');
                $action = 'edit';
            }
        } else {
            $action = 'edit';
        }
    }
}

if ($action == 'confirm_delete' && $confirm == 'yes') {
    if ($object->fetch($id) > 0) {
        $result = $object->delete($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberDeleted"), null, 'mesgs');
            $action = '';
        } else {
            setEventMessages($object->error, $object->errors, 'errors');
        }
    }
}

if ($action == 'activate') {
    if ($object->fetch($id) > 0) {
        $object->active = 1;
        $result = $object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberActivated"), null, 'mesgs');
        }
    }
}

if ($action == 'disable') {
    if ($object->fetch($id) > 0) {
        $object->active = 0;
        $result = $object->update($user);
        if ($result > 0) {
            setEventMessages($langs->trans("CommitteeMemberDisabled"), null, 'mesgs');
        }
    }
}

/*
 * View
 */

$form = new Form($db);

$title = $langs->trans('LoanCommittee');
$help_url = '';

llxHeader('', $title, $help_url);

$linkback = '<a href="'.dol_buildpath('/employerloan/admin/employerloan.php', 1).'">'.$langs->trans("BackToModuleList").'</a>';
print load_fiche_titre($title, $linkback, 'title_setup');

// Configuration header
$head = array();
$head[0][0] = dol_buildpath('/employerloan/admin/employerloan.php', 1);
$head[0][1] = $langs->trans('Setup');
$head[0][2] = 'setup';

$head[1][0] = dol_buildpath('/employerloan/criteria_setup.php', 1);
$head[1][1] = $langs->trans('EvaluationCriteria');
$head[1][2] = 'criteria';

$head[2][0] = dol_buildpath('/employerloan/committee_setup.php', 1);
$head[2][1] = $langs->trans('LoanCommittee');
$head[2][2] = 'committee';

print dol_get_fiche_head($head, 'committee', '', -1);

// Buttons
if ($action != 'create' && $action != 'edit') {
    print '<div class="tabsAction">';
    print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?action=create">'.$langs->trans('AddCommitteeMember').'</a>';
    print '</div>';
}

// Form to create/edit committee member
if ($action == 'create' || $action == 'edit') {
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="'.($action == 'create' ? 'add' : 'update').'">';
    if ($action == 'edit') {
        print '<input type="hidden" name="id" value="'.$id.'">';
        $object->fetch($id);
    }
    
    print '<table class="border centpercent">';
    
    // User
    print '<tr><td class="fieldrequired">'.$langs->trans('User').'</td>';
    print '<td>'.$form->select_dolusers(($action == 'edit' ? $object->fk_user : GETPOST('fk_user', 'int')), 'fk_user', 1, '', 0, '', '', $conf->entity, 0, 0, '', 0, '', 'maxwidth300').'</td></tr>';
    
    // Role
    print '<tr><td class="fieldrequired">'.$langs->trans('Role').'</td>';
    $roles = array(
        'president' => $langs->trans('President'),
        'member' => $langs->trans('Member'),
        'secretary' => $langs->trans('Secretary'),
        'observer' => $langs->trans('Observer')
    );
    print '<td>'.$form->selectarray('role', $roles, ($action == 'edit' ? $object->role : GETPOST('role', 'alpha')), 1).'</td></tr>';
    
    // Max amount
    print '<tr><td>'.$langs->trans('MaxAmount').'</td>';
    print '<td><input type="number" name="max_amount" step="0.01" min="0" value="'.($action == 'edit' ? $object->max_amount : GETPOST('max_amount', 'numeric')).'" placeholder="'.$langs->trans('NoLimit').'"></td></tr>';
    
    // Active
    print '<tr><td>'.$langs->trans('Active').'</td>';
    print '<td>'.$form->selectyesno('active', ($action == 'edit' ? $object->active : 1), 1).'</td></tr>';
    
    print '</table>';
    
    print '<div class="center">';
    print '<input type="submit" class="button" value="'.($action == 'create' ? $langs->trans('Add') : $langs->trans('Modify')).'">';
    print ' &nbsp; ';
    print '<input type="submit" class="button button-cancel" name="cancel" value="'.$langs->trans('Cancel').'">';
    print '</div>';
    
    print '</form>';
}

// List of committee members
if ($action != 'create' && $action != 'edit') {
    $sql = "SELECT c.rowid, c.fk_user, c.role, c.max_amount, c.active, c.date_creation,";
    $sql .= " u.lastname, u.firstname, u.login";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_committee as c";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user as u ON c.fk_user = u.rowid";
    $sql .= " WHERE c.entity = ".$conf->entity;
    $sql .= " ORDER BY c.role ASC, u.lastname ASC";
    
    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);
        
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th>'.$langs->trans('User').'</th>';
        print '<th>'.$langs->trans('Role').'</th>';
        print '<th>'.$langs->trans('MaxAmount').'</th>';
        print '<th>'.$langs->trans('Status').'</th>';
        print '<th>'.$langs->trans('DateCreation').'</th>';
        print '<th width="60">'.$langs->trans('Action').'</th>';
        print '</tr>';
        
        if ($num > 0) {
            $i = 0;
            while ($i < $num) {
                $obj = $db->fetch_object($resql);
                
                print '<tr class="oddeven">';
                print '<td>';
                $userstatic = new User($db);
                $userstatic->id = $obj->fk_user;
                $userstatic->lastname = $obj->lastname;
                $userstatic->firstname = $obj->firstname;
                $userstatic->login = $obj->login;
                print $userstatic->getNomUrl(1);
                print '</td>';
                print '<td>';
                $roles = array(
                    'president' => $langs->trans('President'),
                    'member' => $langs->trans('Member'),
                    'secretary' => $langs->trans('Secretary'),
                    'observer' => $langs->trans('Observer')
                );
                print (isset($roles[$obj->role]) ? $roles[$obj->role] : $obj->role);
                print '</td>';
                print '<td class="amount">'.($obj->max_amount ? price($obj->max_amount) : $langs->trans('NoLimit')).'</td>';
                print '<td class="center">';
                if ($obj->active) {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?action=disable&id='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Enabled"), 'switch_on');
                    print '</a>';
                } else {
                    print '<a href="'.$_SERVER["PHP_SELF"].'?action=activate&id='.$obj->rowid.'&token='.newToken().'">';
                    print img_picto($langs->trans("Disabled"), 'switch_off');
                    print '</a>';
                }
                print '</td>';
                print '<td>'.dol_print_date($db->jdate($obj->date_creation), 'day').'</td>';
                print '<td class="center">';
                print '<a href="'.$_SERVER["PHP_SELF"].'?action=edit&id='.$obj->rowid.'">';
                print img_edit();
                print '</a>';
                print ' ';
                print '<a href="'.$_SERVER["PHP_SELF"].'?action=delete&id='.$obj->rowid.'&token='.newToken().'" onclick="return confirm(\''.$langs->trans('ConfirmDeleteCommitteeMember').'\');">';
                print img_delete();
                print '</a>';
                print '</td>';
                print '</tr>';
                
                $i++;
            }
        } else {
            print '<tr><td colspan="6"><span class="opacitymedium">'.$langs->trans("NoCommitteeMemberFound").'</span></td></tr>';
        }
        
        print '</table>';
        
        $db->free($resql);
    } else {
        dol_print_error($db);
    }
}

print dol_get_fiche_end();

// End of page
llxFooter();
