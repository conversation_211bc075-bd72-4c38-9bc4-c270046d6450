<?php
/**
 * Script pour créer la table manquante employer_loan_schedule
 */

require_once '../../main.inc.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/admin.lib.php';

// Vérification des permissions
if (!$user->admin) {
    accessforbidden();
}

$action = GETPOST('action', 'alpha');

print '<html><head><title>Création table employer_loan_schedule</title></head><body>';
print '<h1>Création de la table manquante employer_loan_schedule</h1>';

if ($action == 'create') {
    print '<h2>Création de la table...</h2>';
    
    // Vérifier si la table existe déjà
    $sql_check = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."employer_loan_schedule'";
    $resql_check = $db->query($sql_check);
    
    if ($resql_check && $db->num_rows($resql_check) > 0) {
        print '<div style="color: orange;">⚠ La table '.MAIN_DB_PREFIX.'employer_loan_schedule existe déjà.</div>';
    } else {
        // Créer la table
        $sql_create = "CREATE TABLE ".MAIN_DB_PREFIX."employer_loan_schedule (
            rowid int(11) NOT NULL AUTO_INCREMENT,
            fk_loan int(11) DEFAULT NULL,
            datec datetime DEFAULT NULL,
            tms timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            datep datetime DEFAULT NULL,
            amount_capital double(24,8) DEFAULT 0.********,
            amount_insurance double(24,8) DEFAULT 0.********,
            amount_interest double(24,8) DEFAULT 0.********,
            fk_typepayment int(11) NOT NULL,
            num_payment varchar(50) DEFAULT NULL,
            note_private text DEFAULT NULL,
            note_public text DEFAULT NULL,
            fk_bank int(11) NOT NULL,
            fk_payment_loan int(11) DEFAULT NULL,
            fk_user_creat int(11) DEFAULT NULL,
            fk_user_modif int(11) DEFAULT NULL,
            PRIMARY KEY (rowid),
            KEY idx_fk_loan (fk_loan)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $resql_create = $db->query($sql_create);
        
        if ($resql_create) {
            print '<div style="color: green;">✓ Table '.MAIN_DB_PREFIX.'employer_loan_schedule créée avec succès.</div>';
            
            // Ajouter la contrainte de clé étrangère
            $sql_fk = "ALTER TABLE ".MAIN_DB_PREFIX."employer_loan_schedule 
                       ADD CONSTRAINT fk_employer_loan_schedule_loan 
                       FOREIGN KEY (fk_loan) REFERENCES ".MAIN_DB_PREFIX."employer_loan(rowid) 
                       ON DELETE CASCADE ON UPDATE CASCADE";
            
            $resql_fk = $db->query($sql_fk);
            
            if ($resql_fk) {
                print '<div style="color: green;">✓ Contrainte de clé étrangère ajoutée avec succès.</div>';
            } else {
                print '<div style="color: orange;">⚠ Contrainte de clé étrangère non ajoutée: '.$db->lasterror().'</div>';
                print '<p>La table fonctionne quand même, mais sans contrainte d\'intégrité référentielle.</p>';
            }
            
            // Vérifier que la table a été créée
            $sql_verify = "DESCRIBE ".MAIN_DB_PREFIX."employer_loan_schedule";
            $resql_verify = $db->query($sql_verify);
            
            if ($resql_verify) {
                print '<h3>Structure de la table créée :</h3>';
                print '<table border="1" style="border-collapse: collapse;">';
                print '<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>';
                
                while ($obj = $db->fetch_object($resql_verify)) {
                    print '<tr>';
                    print '<td>'.$obj->Field.'</td>';
                    print '<td>'.$obj->Type.'</td>';
                    print '<td>'.$obj->Null.'</td>';
                    print '<td>'.$obj->Key.'</td>';
                    print '<td>'.$obj->Default.'</td>';
                    print '</tr>';
                }
                print '</table>';
            }
            
            print '<h3>Prochaines étapes :</h3>';
            print '<ul>';
            print '<li>✓ La table employer_loan_schedule est maintenant créée</li>';
            print '<li>✓ Vous pouvez maintenant relancer les tests : <a href="test_loan_corrections.php">test_loan_corrections.php</a></li>';
            print '<li>✓ Ou vérifier les corrections : <a href="fix_loan_behavior.php">fix_loan_behavior.php</a></li>';
            print '</ul>';
            
        } else {
            print '<div style="color: red;">✗ Erreur lors de la création de la table : '.$db->lasterror().'</div>';
        }
    }
    
} else {
    print '<p>Ce script va créer la table manquante <strong>employer_loan_schedule</strong> nécessaire au bon fonctionnement du module employerloan.</p>';
    
    print '<h3>Informations sur la table à créer :</h3>';
    print '<ul>';
    print '<li><strong>Nom :</strong> '.MAIN_DB_PREFIX.'employer_loan_schedule</li>';
    print '<li><strong>Usage :</strong> Stockage des échéanciers de remboursement des prêts employés</li>';
    print '<li><strong>Champs principaux :</strong> fk_loan, datep, amount_capital, amount_interest, amount_insurance</li>';
    print '<li><strong>Clé étrangère :</strong> fk_loan vers '.MAIN_DB_PREFIX.'employer_loan(rowid)</li>';
    print '</ul>';
    
    // Vérifier si la table existe déjà
    $sql_check = "SHOW TABLES LIKE '".MAIN_DB_PREFIX."employer_loan_schedule'";
    $resql_check = $db->query($sql_check);
    
    if ($resql_check && $db->num_rows($resql_check) > 0) {
        print '<div style="color: orange; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7;">⚠ La table existe déjà dans la base de données.</div>';
    } else {
        print '<div style="color: red; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb;">✗ La table n\'existe pas encore dans la base de données.</div>';
        print '<p><a href="?action=create" class="button" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none;">Créer la table</a></p>';
    }
    
    print '<h3>Vérification des autres tables :</h3>';
    
    $tables_to_check = [
        'employer_loan' => 'Table principale des prêts employés',
        'employer_payment_loan' => 'Table des paiements des prêts employés'
    ];
    
    foreach ($tables_to_check as $table => $description) {
        $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
        $resql = $db->query($sql);
        if ($resql && $db->num_rows($resql) > 0) {
            print "✓ <strong>".$table."</strong> : ".$description." - <span style='color: green;'>Existe</span><br>";
        } else {
            print "✗ <strong>".$table."</strong> : ".$description." - <span style='color: red;'>Manquante</span><br>";
        }
    }
}

print '</body></html>';
