<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * \file    loan_request_list.php
 * \ingroup employerloan
 * \brief   List page for loan requests
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
dol_include_once('/employerloan/class/employerloan_request.class.php');

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "other"));

// Get parameters
$action = GETPOST('action', 'aZ09');
$massaction = GETPOST('massaction', 'alpha');
$show_files = GETPOST('show_files', 'int');
$confirm = GETPOST('confirm', 'alpha');
$cancel = GETPOST('cancel', 'aZ09');
$toselect = GETPOST('toselect', 'array');
$contextpage = GETPOST('contextpage', 'aZ') ? GETPOST('contextpage', 'aZ') : 'loanrequestlist';

// Initialize technical objects
$object = new EmployerLoanRequest($db);
$extrafields = new ExtraFields($db);
$diroutputmassaction = $conf->employerloan->dir_output.'/temp/massgeneration/'.$user->id;
$hookmanager->initHooks(array('loanrequestlist'));

// Fetch optionals attributes and labels
$extrafields->fetch_name_optionals_label($object->table_element);

$search_array_options = $extrafields->getOptionalsFromPost($object->table_element, '', 'search_');

// List of fields to search into when doing a "search in all"
$fieldstosearchall = array();

// Definition of array of fields for columns
$arrayfields = array();
$arrayfields['lr.ref'] = array('label'=>"Ref", 'checked'=>1, 'position'=>10);
$arrayfields['u.lastname'] = array('label'=>"Employee", 'checked'=>1, 'position'=>20);
$arrayfields['lr.amount_requested'] = array('label'=>"Amount", 'checked'=>1, 'position'=>30);
$arrayfields['lr.purpose'] = array('label'=>"Purpose", 'checked'=>1, 'position'=>40);
$arrayfields['lr.date_request'] = array('label'=>"Date Request", 'checked'=>1, 'position'=>50);
$arrayfields['lr.status'] = array('label'=>"Status", 'checked'=>1, 'position'=>60);
$arrayfields['lr.evaluation_score'] = array('label'=>"Score", 'checked'=>1, 'position'=>70);

// Security check
if (!$user->hasRight('employerloan', 'read')) {
    accessforbidden();
}

$permissiontoread = $user->hasRight('employerloan', 'read');
$permissiontoadd = $user->hasRight('employerloan', 'write');
$permissiontodelete = $user->hasRight('employerloan', 'delete');

// Mass actions
$arrayofmassactions = array();
if ($permissiontodelete) {
    $arrayofmassactions['predelete'] = img_picto('', 'delete', 'class="pictofixedwidth"').$langs->trans("Delete");
}
if (GETPOST('nomassaction', 'int') || in_array($massaction, array('presend', 'predelete'))) {
    $arrayofmassactions = array();
}
$massactionbutton = $form->selectMassAction('', $arrayofmassactions);

/*
 * Actions
 */

if (GETPOST('cancel', 'alpha')) {
    $action = 'list';
    $massaction = '';
}
if (!GETPOST('confirmmassaction', 'alpha') && $massaction != 'presend' && $massaction != 'confirm_presend') {
    $massaction = '';
}

$parameters = array();
$reshook = $hookmanager->executeHooks('doActions', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks
if ($reshook < 0) {
    setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
}

if (empty($reshook)) {
    // Selection of new fields
    include DOL_DOCUMENT_ROOT.'/core/actions_changeselectedfields.inc.php';

    // Purge search criteria
    if (GETPOST('button_removefilter_x', 'alpha') || GETPOST('button_removefilter.x', 'alpha') || GETPOST('button_removefilter', 'alpha')) { // All tests are required to be compatible with all browsers
        foreach ($object->fields as $key => $val) {
            $search[$key] = '';
            if (preg_match('/^(date|timestamp|datetime)/', $val['type'])) {
                $search[$key.'_dtstart'] = '';
                $search[$key.'_dtend'] = '';
            }
        }
        $toselect = array();
        $search_array_options = array();
    }
    if (GETPOST('button_removefilter_x', 'alpha') || GETPOST('button_removefilter.x', 'alpha') || GETPOST('button_removefilter', 'alpha')
        || GETPOST('button_search_x', 'alpha') || GETPOST('button_search.x', 'alpha') || GETPOST('button_search', 'alpha')) {
        $massaction = ''; // Protection to avoid mass action if we force a new search during a mass action confirmation
    }
}

/*
 * View
 */

$form = new Form($db);

$now = dol_now();

//$help_url="EN:Module_Third_Parties|FR:Module_Tiers|ES:Empresas";
$help_url = '';
$title = $langs->trans('LoanRequestsList');
$morejs = array();
$morecss = array();

// Build and execute select
// --------------------------------------------------------------------
$sql = 'SELECT ';
$sql .= 'lr.rowid, lr.ref, lr.fk_employee, lr.amount_requested, lr.purpose, lr.duration_months, ';
$sql .= 'lr.status, lr.date_request, lr.date_decision, lr.evaluation_score, lr.evaluation_comments, ';
$sql .= 'u.lastname, u.firstname, u.login ';

$sqlfields = $sql; // $sql fields to remove for count total

$sql .= ' FROM '.MAIN_DB_PREFIX.'employer_loan_request as lr';
$sql .= ' LEFT JOIN '.MAIN_DB_PREFIX.'user as u ON lr.fk_employee = u.rowid';
$sql .= ' WHERE lr.entity IN ('.getEntity('employerloan').')';

// Count total nb of records
$nbtotalofrecords = '';
if (!getDolGlobalInt('MAIN_DISABLE_FULL_SCANLIST')) {
    /* The fast and low memory method to get and count full list converts the sql into a sql count */
    $sqlforcount = preg_replace('/^'.preg_quote($sqlfields, '/').'/', 'SELECT COUNT(*) as nbtotalofrecords', $sql);
    $sqlforcount = preg_replace('/GROUP BY .*$/', '', $sqlforcount);
    $resql = $db->query($sqlforcount);
    if ($resql) {
        $objforcount = $db->fetch_object($resql);
        $nbtotalofrecords = $objforcount->nbtotalofrecords;
    } else {
        dol_print_error($db);
    }

    if (($page * $limit) > $nbtotalofrecords) { // if total resultset is smaller than the paging size (filtering), goto and load page 0
        $page = 0;
        $offset = 0;
    }
    $db->free($resql);
}

// Complete request and execute it
$sql .= $db->order($sortfield, $sortorder);
if ($limit) {
    $sql .= $db->plimit($limit + 1, $offset);
}

$resql = $db->query($sql);
if (!$resql) {
    dol_print_error($db);
    exit;
}

$num = $db->num_rows($resql);

// Direct jump if only one record found
if ($num == 1 && !getDolGlobalInt('MAIN_SEARCH_DIRECT_OPEN_IF_ONLY_ONE') && $search_all && !$page) {
    $obj = $db->fetch_object($resql);
    $id = $obj->rowid;
    header("Location: ".dol_buildpath('/employerloan/loan_request_card.php', 1).'?id='.$id);
    exit;
}

// Output page
// --------------------------------------------------------------------

llxHeader('', $title, $help_url, '', 0, 0, $morejs, $morecss, '', 'bodyforlist');

$arrayofselected = is_array($toselect) ? $toselect : array();

$param = '';
if (!empty($mode)) {
    $param .= '&mode='.urlencode($mode);
}
if (!empty($contextpage) && $contextpage != $_SERVER["PHP_SELF"]) {
    $param .= '&contextpage='.urlencode($contextpage);
}
if ($limit > 0 && $limit != $conf->liste_limit) {
    $param .= '&limit='.((int) $limit);
}

$newcardbutton = '';
$newcardbutton .= dolGetButtonTitle($langs->trans('ViewList'), '', 'fa fa-bars imgforviewmode', $_SERVER["PHP_SELF"].'?mode=common'.preg_replace('/(&|\?)*mode=[^&]+/', '', $param), '', ((empty($mode) || $mode == 'common') ? 2 : 1), array('morecss'=>'reposition'));
$newcardbutton .= dolGetButtonTitle($langs->trans('ViewKanban'), '', 'fa fa-th-list imgforviewmode', $_SERVER["PHP_SELF"].'?mode=kanban'.preg_replace('/(&|\?)*mode=[^&]+/', '', $param), '', ($mode == 'kanban' ? 2 : 1), array('morecss'=>'reposition'));
$newcardbutton .= dolGetButtonTitle($langs->trans('NewLoanRequest'), '', 'fa fa-plus-circle', dol_buildpath('/employerloan/loan_request_card.php', 1).'?action=create'.($param ? '&'.$param : ''), '', $permissiontoadd);

print '<form method="POST" id="searchFormList" action="'.$_SERVER["PHP_SELF"].'">';
if ($optioncss != '') {
    print '<input type="hidden" name="optioncss" value="'.$optioncss.'">';
}
print '<input type="hidden" name="token" value="'.newToken().'">';
print '<input type="hidden" name="formfilteraction" id="formfilteraction" value="list">';
print '<input type="hidden" name="action" value="list">';
print '<input type="hidden" name="sortfield" value="'.$sortfield.'">';
print '<input type="hidden" name="sortorder" value="'.$sortorder.'">';
print '<input type="hidden" name="page" value="'.$page.'">';
print '<input type="hidden" name="contextpage" value="'.$contextpage.'">';
print '<input type="hidden" name="page_y" value="">';
print '<input type="hidden" name="mode" value="'.$mode.'">';

print_barre_liste($title, $page, $_SERVER["PHP_SELF"], $param, $sortfield, $sortorder, $massactionbutton, $num, $nbtotalofrecords, 'object_'.$object->picto, 0, $newcardbutton, '', $limit, 0, 0, 1);

// Add code for pre-mass action (confirmation or email presend form)
$topicmail = "SendLoanRequestRef";
$modelmail = "loanrequest";
$objecttmp = new EmployerLoanRequest($db);
$trackid = 'loanreq'.$object->id;
include DOL_DOCUMENT_ROOT.'/core/tpl/massactions_pre.tpl.php';

if ($search_all) {
    $setupstring = '';
    foreach ($fieldstosearchall as $key => $val) {
        $fieldstosearchall[$key] = $langs->trans($val);
        $setupstring .= $key."=".$val.";";
    }
    print '<!-- Search done like if EMPLOYERLOAN_QUICKSEARCH_ON_FIELDS = '.$setupstring.' -->'."\n";
    print '<div class="divsearchfieldfilter">'.$langs->trans("FilterOnInto", $search_all).join(', ', $fieldstosearchall).'</div>';
}

$moreforfilter = '';

$parameters = array();
$reshook = $hookmanager->executeHooks('printFieldPreListTitle', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
if (empty($reshook)) {
    $moreforfilter .= $hookmanager->resPrint;
} else {
    $moreforfilter = $hookmanager->resPrint;
}

if (!empty($moreforfilter)) {
    print '<div class="liste_titre liste_titre_bydiv centpercent">';
    print $moreforfilter;
    print '</div>';
}

$varpage = empty($contextpage) ? $_SERVER["PHP_SELF"] : $contextpage;
$selectedfields = ($mode != 'kanban' ? $form->multiSelectArrayWithCheckbox('selectedfields', $arrayfields, $varpage, getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) : ''); // This also change content of $arrayfields
$selectedfields .= (is_array($arrayofmassactions) && count($arrayofmassactions) ? $form->showCheckAddButtons('checkforselect', 1) : '');

print '<div class="div-table-responsive">'; // You can use div-table-responsive-no-min if you don't need reserved height for your table
print '<table class="tagtable nobottomiftotal liste'.($moreforfilter ? " listwithfilterbefore" : "").'">'."\n";

// Fields title search
// --------------------------------------------------------------------
print '<tr class="liste_titre_filter">';
// Action column
if (getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) {
    print '<td class="liste_titre maxwidthsearch">';
    $searchpicto = $form->showFilterButtons('left');
    print $searchpicto;
    print '</td>';
}
foreach ($object->fields as $key => $val) {
    $searchkey = (empty($search[$key]) ? '' : $search[$key]);
    if (!empty($arrayfields['t.'.$key]['checked'])) {
        print '<td class="liste_titre">';
        if (!empty($val['arrayofkeyval']) && is_array($val['arrayofkeyval'])) {
            print $form->selectarray('search_'.$key, $val['arrayofkeyval'], (isset($search[$key]) ? $search[$key] : ''), $val['notnull'], 0, 0, '', 1, 0, 0, '', 'maxwidth100', 1);
        } elseif ((strpos($val['type'], 'integer:') === 0) || (strpos($val['type'], 'sellist:') === 0)) {
            print $object->showInputField($val, $key, (isset($search[$key]) ? $search[$key] : ''), '', '', 'search_', 'maxwidth125', 1);
        } elseif (preg_match('/^(date|timestamp|datetime)/', $val['type'])) {
            print '<div class="nowrap">';
            print $form->selectDate($search[$key.'_dtstart'] ? $search[$key.'_dtstart'] : '', "search_".$key."_dtstart", 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('From'));
            print '</div>';
            print '<div class="nowrap">';
            print $form->selectDate($search[$key.'_dtend'] ? $search[$key.'_dtend'] : '', "search_".$key."_dtend", 0, 0, 1, '', 1, 0, 0, '', '', '', '', 1, '', $langs->trans('to'));
            print '</div>';
        } elseif ($key == 'lang') {
            require_once DOL_DOCUMENT_ROOT.'/core/class/html.formadmin.class.php';
            $formadmin = new FormAdmin($db);
            print $formadmin->select_language($search[$key], 'search_lang', 0, null, 1, 0, 0, 'minwidth100imp maxwidth125', 2);
        } else {
            print '<input type="text" class="flat maxwidth75" name="search_'.$key.'" value="'.dol_escape_htmltag(isset($search[$key]) ? $search[$key] : '').'">';
        }
        print '</td>';
    }
}
// Extra fields
include DOL_DOCUMENT_ROOT.'/core/tpl/extrafields_list_search_input.tpl.php';

// Fields from hook
$parameters = array('arrayfields'=>$arrayfields);
$reshook = $hookmanager->executeHooks('printFieldListOption', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
print $hookmanager->resPrint;
// Action column
if (!getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) {
    print '<td class="liste_titre maxwidthsearch">';
    $searchpicto = $form->showFilterButtons();
    print $searchpicto;
    print '</td>';
}
print '</tr>'."\n";

$totalarray = array();
$totalarray['nbfield'] = 0;

// Fields title label
// --------------------------------------------------------------------
print '<tr class="liste_titre">';
// Action column
if (getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) {
    print getTitleFieldOfList($selectedfields, 0, $_SERVER["PHP_SELF"], '', '', '', '', $sortfield, $sortorder, 'center maxwidthsearch ')."\n";
    $totalarray['nbfield']++;
}
foreach ($arrayfields as $key => $val) {
    if (!empty($val['checked'])) {
        print getTitleFieldOfList($val['label'], 0, $_SERVER["PHP_SELF"], $key, '', $param, (($val['align']) ? 'align='.$val['align'] : ''), $sortfield, $sortorder, ($val['csslist'] ? $val['csslist'].' ' : ''), 0, (empty($val['helplist']) ? '' : $val['helplist']))."\n";
        $totalarray['nbfield']++;
    }
}
// Extra fields
include DOL_DOCUMENT_ROOT.'/core/tpl/extrafields_list_search_title.tpl.php';
// Hook fields
$parameters = array('arrayfields'=>$arrayfields, 'param'=>$param, 'sortfield'=>$sortfield, 'sortorder'=>$sortorder, 'totalarray'=>&$totalarray);
$reshook = $hookmanager->executeHooks('printFieldListTitle', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
print $hookmanager->resPrint;
// Action column
if (!getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) {
    print getTitleFieldOfList($selectedfields, 0, $_SERVER["PHP_SELF"], '', '', '', '', $sortfield, $sortorder, 'center maxwidthsearch ')."\n";
    $totalarray['nbfield']++;
}
print '</tr>'."\n";

// Detect if we need a fetch on each output line
$needToFetchEachLine = 0;
if (isset($extrafields->attributes[$object->table_element]['computed']) && is_array($extrafields->attributes[$object->table_element]['computed']) && count($extrafields->attributes[$object->table_element]['computed']) > 0) {
    foreach ($extrafields->attributes[$object->table_element]['computed'] as $key => $val) {
        if (!is_null($val) && preg_match('/\$object/', $val)) {
            $needToFetchEachLine++; // There is at least one compute field that use $object
        }
    }
}

// Loop on record
// --------------------------------------------------------------------
$i = 0;
$savnbfield = $totalarray['nbfield'];
$totalarray = array();
$totalarray['nbfield'] = 0;
$imaxinloop = ($limit ? min($num, $limit) : $num);
while ($i < $imaxinloop) {
    $obj = $db->fetch_object($resql);
    if (empty($obj)) {
        break; // Should not happen
    }

    // Store properties in $object
    $object->setVarsFromFetchObj($obj);

    if ($mode == 'kanban') {
        if ($i == 0) {
            $save_lastsearch_value = -1;
            $massactionbutton = '';

            print '<tr class="trkanban"><td colspan="'.$savnbfield.'">';
            print '<div class="box-flex-container kanban">';
        }
        // Output Kanban
        print $object->getKanbanView('', array('object'=>$obj, 'selected'=>(is_array($arrayofselected) && in_array($object->id, $arrayofselected))));
        if ($i == ($imaxinloop - 1)) {
            print '</div>';
            print '</td></tr>';
        }
    } else {
        // Show here line of result
        $j = 0;
        print '<tr data-rowid="'.$object->id.'" class="oddeven">';

        // Action column
        if (getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) {
            print '<td class="nowrap center">';
            if ($massactionbutton || $massaction) { // If we are in select mode (massactionbutton defined) or if we have already selected and sent an action ($massaction) defined
                $selected = 0;
                if (is_array($arrayofselected) && in_array($object->id, $arrayofselected)) {
                    $selected = 1;
                }
                print '<input id="cb'.$object->id.'" class="flat checkforselect" type="checkbox" name="toselect[]" value="'.$object->id.'"'.($selected ? ' checked="checked"' : '').'>';
            }
            print '</td>';
            if (!$i) {
                $totalarray['nbfield']++;
            }
        }

        foreach ($arrayfields as $key => $val) {
            if (!empty($val['checked'])) {
                print '<td'.($val['align'] ? ' align="'.$val['align'].'"' : '');
                if (!empty($val['csslist'])) {
                    print ' class="'.$val['csslist'].'"';
                }
                print '>';
                if ($key == 'lr.ref') {
                    print '<a href="'.dol_buildpath('/employerloan/loan_request_card.php', 1).'?id='.$obj->rowid.'">';
                    print $obj->ref;
                    print '</a>';
                } elseif ($key == 'u.lastname') {
                    print $obj->firstname.' '.$obj->lastname;
                } elseif ($key == 'lr.amount_requested') {
                    print price($obj->amount_requested);
                } elseif ($key == 'lr.purpose') {
                    print dol_trunc($obj->purpose, 50);
                } elseif ($key == 'lr.date_request') {
                    print dol_print_date($db->jdate($obj->date_request), 'day');
                } elseif ($key == 'lr.status') {
                    $status_array = array(
                        0 => array('label' => 'Pending', 'color' => 'status4'),
                        1 => array('label' => 'Under Review', 'color' => 'status1'),
                        2 => array('label' => 'Approved', 'color' => 'status6'),
                        3 => array('label' => 'Rejected', 'color' => 'status8')
                    );
                    if (isset($status_array[$obj->status])) {
                        print dolGetStatus($status_array[$obj->status]['label'], '', '', $status_array[$obj->status]['color'], 1);
                    }
                } elseif ($key == 'lr.evaluation_score') {
                    if ($obj->evaluation_score) {
                        print number_format($obj->evaluation_score, 2).'/10';
                    }
                } else {
                    print $obj->$key;
                }
                print '</td>';
                if (!$i) {
                    $totalarray['nbfield']++;
                }
            }
        }

        // Extra fields
        include DOL_DOCUMENT_ROOT.'/core/tpl/extrafields_list_print_fields.tpl.php';
        // Fields from hook
        $parameters = array('arrayfields'=>$arrayfields, 'object'=>$obj, 'obj'=>$obj, 'i'=>$i, 'totalarray'=>&$totalarray);
        $reshook = $hookmanager->executeHooks('printFieldListValue', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
        print $hookmanager->resPrint;

        // Action column
        if (!getDolGlobalString('MAIN_CHECKBOX_LEFT_COLUMN')) {
            print '<td class="nowrap center">';
            if ($massactionbutton || $massaction) { // If we are in select mode (massactionbutton defined) or if we have already selected and sent an action ($massaction) defined
                $selected = 0;
                if (is_array($arrayofselected) && in_array($object->id, $arrayofselected)) {
                    $selected = 1;
                }
                print '<input id="cb'.$object->id.'" class="flat checkforselect" type="checkbox" name="toselect[]" value="'.$object->id.'"'.($selected ? ' checked="checked"' : '').'>';
            }
            print '</td>';
            if (!$i) {
                $totalarray['nbfield']++;
            }
        }

        print '</tr>'."\n";
    }

    $i++;
}

// Show total line
include DOL_DOCUMENT_ROOT.'/core/tpl/list_print_total.tpl.php';

// If no record found
if ($num == 0) {
    $colspan = 1;
    foreach ($arrayfields as $key => $val) {
        if (!empty($val['checked'])) {
            $colspan++;
        }
    }
    print '<tr><td colspan="'.$colspan.'"><span class="opacitymedium">'.$langs->trans("NoRecordFound").'</span></td></tr>';
}

$db->free($resql);

$parameters = array('arrayfields'=>$arrayfields, 'sql'=>$sql);
$reshook = $hookmanager->executeHooks('printFieldListFooter', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
print $hookmanager->resPrint;

print '</table>'."\n";
print '</div>'."\n";

print '</form>'."\n";

if (is_array($arrayofmassactions) && in_array('builddoc', array_keys($arrayofmassactions)) && ($nbtotalofrecords === '' || $nbtotalofrecords)) {
    $hidegeneratedfilelistifempty = 1;
    if ($massaction == 'builddoc' || $action == 'remove_file' || $show_files) {
        $hidegeneratedfilelistifempty = 0;
    }

    require_once DOL_DOCUMENT_ROOT.'/core/class/html.formfile.class.php';
    $formfile = new FormFile($db);

    // Show list of available documents
    $urlsource = $_SERVER['PHP_SELF'].'?sortfield='.$sortfield.'&sortorder='.$sortorder;
    $urlsource .= str_replace('&amp;', '&', $param);

    $filedir = $diroutputmassaction;
    $genallowed = $permissiontoread;
    $delallowed = $permissiontoadd;

    print $formfile->showdocuments('massfilesarea_employerloan', '', $filedir, $urlsource, 0, $delallowed, '', 1, 1, 0, 48, 1, $param, $title, '', '', '', null, $hidegeneratedfilelistifempty);
}

// End of page
llxFooter();
