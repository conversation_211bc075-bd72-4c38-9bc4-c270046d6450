<?php
/* Module declaration for EmployerLoan */
include_once DOL_DOCUMENT_ROOT.'/core/modules/DolibarrModules.class.php';

class modEmployerLoan extends DolibarrModules
{
    public function __construct($db)
    {
        global $conf, $langs;

        $this->db = $db;

        // Id for module (must be unique)
        $this->numero = 180000;

        // Key text used to identify module (for permissions, menus, etc...)
        $this->rights_class = 'employerloan';

        // Family can be 'base' (core modules),'crm','financial','hr','projects','products','ecm','technic' (transverse modules),'interface' (interface modules),'other','...'
        $this->family = "hr";

        // Module position in the family on 2 digits ('01', '10', '20', ...)
        $this->module_position = '90';

        // Module label (no space allowed), used if translation string 'ModuleEmployerLoanName' not found
        $this->name = preg_replace('/^mod/i', '', get_class($this));

        // Module description, used if translation string 'ModuleEmployerLoanDesc' not found
        $this->description = "Gestion des crédits accordés aux salariés";
        $this->descriptionlong = "Module de gestion des crédits accordés aux salariés avec workflow complet";

        // Author
        $this->editor_name = 'NextGestion';
        $this->editor_url = 'https://www.nextgestion.com';

        // Possible values for version are: 'development', 'experimental', 'dolibarr', 'dolibarr_deprecated' or a version string like 'x.y.z'
        $this->version = '1.0.0';

        // Key used in llx_const table to save module status enabled/disabled
        $this->const_name = 'MAIN_MODULE_'.strtoupper($this->name);

        // Name of image file used for this module
        $this->picto = 'money-bill-alt';

        // Define some features supported by module
        $this->module_parts = array(
            'triggers' => 0,
            'login' => 0,
            'substitutions' => 0,
            'menus' => 1,
            'tpl' => 0,
            'barcode' => 0,
            'models' => 0,
            'printing' => 0,
            'theme' => 0,
            'css' => array(),
            'js' => array(),
            'hooks' => array(
                'data' => array(
                    'usercard',
                    'salarycard'
                ),
                'entity' => '0'
            ),
            'moduleforexternal' => 0,
        );

        // Data directories to create when module is enabled
        $this->dirs = array("/employerloan/temp");

        // Config pages
        $this->config_page_url = array('employerloan.php@employerloan');

        // Dependencies
        $this->hidden = false;
        $this->depends = array();
        $this->requiredby = array();
        $this->conflictwith = array();

        // The language file dedicated to your module
        $this->langfiles = array("employerloan@employerloan");

        // Prerequisites
        $this->phpmin = array(7, 0);
        $this->need_dolibarr_version = array(11, -3);

        // Messages at activation
        $this->warnings_activation = array();
        $this->warnings_activation_ext = array();

        // Constants
        $this->const = array(
            1 => array('EMPLOYERLOAN_ADDON', 'chaine', 'mod_employerloan_standard', 'Name of PDF model of employerloan', 0),
        );

        // Array to add new pages in new tabs
        $this->tabs = array();

        // Dictionaries
        $this->dictionaries = array();

        // Boxes/Widgets
        $this->boxes = array();

        // Cronjobs
        $this->cronjobs = array();

        // Permissions provided by this module
        $this->rights = array();
        $r = 0;
        $r++;
        $this->rights[$r][0] = 180001;
        $this->rights[$r][1] = 'Lire les crédits salariés';
        $this->rights[$r][2] = 'r';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'read';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[$r][0] = 180002;
        $this->rights[$r][1] = 'Créer/modifier les crédits salariés';
        $this->rights[$r][2] = 'w';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'write';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[$r][0] = 180003;
        $this->rights[$r][1] = 'Supprimer les crédits salariés';
        $this->rights[$r][2] = 'd';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'delete';
        $this->rights[$r][5] = '';
        // Main menu entries to add
        $this->menu = array();
        $r = 0;

        // Add here entries to declare new menus
        $this->menu[$r] = array(
            'fk_menu'=>'fk_mainmenu=hrm',
            'type'=>'left',
            'titre'=>'Crédits salariés',
            'mainmenu'=>'hrm',
            'leftmenu'=>'employerloan',
            'url'=>'/custom/employerloan/list.php',
            'langs'=>'employerloan@employerloan',
            'position'=>1000 + $r,
            'enabled'=>'$user->rights->employerloan->read',
            'perms'=>'$user->rights->employerloan->read',
            'target'=>'',
            'user'=>0,
        );
        $r++;

        $this->menu[$r] = array(
            'fk_menu'=>'fk_mainmenu=hrm,fk_leftmenu=employerloan',
            'type'=>'left',
            'titre'=>'Liste des crédits',
            'mainmenu'=>'hrm',
            'leftmenu'=>'employerloan_list',
            'url'=>'/custom/employerloan/list.php',
            'langs'=>'employerloan@employerloan',
            'position'=>1000 + $r,
            'enabled'=>'$user->rights->employerloan->read',
            'perms'=>'$user->rights->employerloan->read',
            'target'=>'',
            'user'=>0,
        );
        $r++;

        $this->menu[$r] = array(
            'fk_menu'=>'fk_mainmenu=hrm,fk_leftmenu=employerloan',
            'type'=>'left',
            'titre'=>'Nouveau crédit',
            'mainmenu'=>'hrm',
            'leftmenu'=>'employerloan_new',
            'url'=>'/custom/employerloan/card.php?action=create',
            'langs'=>'employerloan@employerloan',
            'position'=>1000 + $r,
            'enabled'=>'$user->rights->employerloan->write',
            'perms'=>'$user->rights->employerloan->write',
            'target'=>'',
            'user'=>0,
        );
        $r++;

        $this->menu[$r] = array(
            'fk_menu'=>'fk_mainmenu=hrm,fk_leftmenu=employerloan',
            'type'=>'left',
            'titre'=>'Demandes de crédit',
            'mainmenu'=>'hrm',
            'leftmenu'=>'employerloan_requests',
            'url'=>'/custom/employerloan/loan_request_list.php',
            'langs'=>'employerloan@employerloan',
            'position'=>1000 + $r,
            'enabled'=>'$user->rights->employerloan->read',
            'perms'=>'$user->rights->employerloan->read',
            'target'=>'',
            'user'=>0,
        );
        $r++;

        $this->menu[$r] = array(
            'fk_menu'=>'fk_mainmenu=hrm,fk_leftmenu=employerloan',
            'type'=>'left',
            'titre'=>'Validation comité',
            'mainmenu'=>'hrm',
            'leftmenu'=>'employerloan_committee_decision',
            'url'=>'/custom/employerloan/committee_decision.php',
            'langs'=>'employerloan@employerloan',
            'position'=>1000 + $r,
            'enabled'=>'$user->rights->employerloan->write',
            'perms'=>'$user->rights->employerloan->write',
            'target'=>'',
            'user'=>0,
        );
        $r++;

        // Menu pour les traites de prêt salarié
        $this->menu[$r] = array(
            'fk_menu'=>'fk_mainmenu=hrm,fk_leftmenu=employerloan',
            'type'=>'left',
            'titre'=>'Traites de prêt',
            'mainmenu'=>'hrm',
            'leftmenu'=>'employerloan_traites',
            'url'=>'/custom/lcr/traites_pret_salarie.php',
            'langs'=>'employerloan@employerloan',
            'position'=>1000 + $r,
            'enabled'=>'$user->rights->employerloan->read',
            'perms'=>'$user->rights->employerloan->read',
            'target'=>'',
            'user'=>0,
        );
        $r++;






    }

    /**
     * Function called when module is enabled.
     * The init function add constants, boxes, permissions and menus (defined in constructor) into Dolibarr database.
     * It also creates data directories
     *
     * @param string $options Options when enabling module ('', 'noboxes')
     * @return int             1 if OK, 0 if KO
     */
    public function init($options = '')
    {
        global $conf, $langs;

        // Remove permissions and default values
        $this->remove($options);

        // SQL commands to execute when enabling module
        $sql = array();

        // Load SQL file for table creation
        $sqlfile = DOL_DOCUMENT_ROOT.'/custom/employerloan/sql/employerloan_tables.sql';
        if (file_exists($sqlfile)) {
            $sqlcontent = file_get_contents($sqlfile);
            if ($sqlcontent) {
                // Clean SQL content - remove comments and empty lines
                $lines = explode("\n", $sqlcontent);
                $cleanedSql = '';
                foreach ($lines as $line) {
                    $line = trim($line);
                    // Skip empty lines and comment lines
                    if (!empty($line) && !preg_match('/^--/', $line)) {
                        $cleanedSql .= $line . "\n";
                    }
                }

                // Split SQL commands by semicolon
                $sqlcommands = explode(';', $cleanedSql);
                foreach ($sqlcommands as $sqlcommand) {
                    $sqlcommand = trim($sqlcommand);
                    if (!empty($sqlcommand)) {
                        $sql[] = $sqlcommand;
                    }
                }
            }
        }

        // Load SQL file for improvements
        $sqlfile2 = DOL_DOCUMENT_ROOT.'/custom/employerloan/sql/employerloan_ameliorations.sql';
        if (file_exists($sqlfile2)) {
            $sqlcontent2 = file_get_contents($sqlfile2);
            if ($sqlcontent2) {
                // Clean SQL content - remove comments and empty lines
                $lines2 = explode("\n", $sqlcontent2);
                $cleanedSql2 = '';
                foreach ($lines2 as $line2) {
                    $line2 = trim($line2);
                    // Skip empty lines and comment lines
                    if (!empty($line2) && !preg_match('/^--/', $line2)) {
                        $cleanedSql2 .= $line2 . "\n";
                    }
                }

                // Split SQL commands by semicolon
                $sqlcommands2 = explode(';', $cleanedSql2);
                foreach ($sqlcommands2 as $sqlcommand2) {
                    $sqlcommand2 = trim($sqlcommand2);
                    if (!empty($sqlcommand2)) {
                        $sql[] = $sqlcommand2;
                    }
                }
            }
        }

        // Create data directories
        $dir = DOL_DATA_ROOT.'/employerloan';
        if (!is_dir($dir)) {
            require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
            dol_mkdir($dir);
        }

        $dir = DOL_DATA_ROOT.'/employerloan/temp';
        if (!is_dir($dir)) {
            require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
            dol_mkdir($dir);
        }

        return $this->_init($sql, $options);
    }

    /**
     * Function called when module is disabled.
     * Remove from database constants, boxes and permissions from Dolibarr database.
     * Data directories are not deleted
     *
     * @param string $options Options when enabling module ('', 'noboxes')
     * @return int             1 if OK, 0 if KO
     */
    public function remove($options = '')
    {
        $sql = array();
        return $this->_remove($sql, $options);
    }
}