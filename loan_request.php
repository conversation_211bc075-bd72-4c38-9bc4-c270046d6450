<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

/**
 * \file    loan_request.php
 * \ingroup employerloan
 * \brief   Page de gestion des demandes de crédit
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/lib/date.lib.php';
require_once DOL_DOCUMENT_ROOT.'/user/class/user.class.php';
dol_include_once('/employerloan/class/employerloan_request.class.php');
dol_include_once('/employerloan/class/employerloan_committee.class.php');

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "other"));

// Get parameters
$id = GETPOST('id', 'int');
$ref = GETPOST('ref', 'alpha');
$action = GETPOST('action', 'aZ09');
$confirm = GETPOST('confirm', 'alpha');
$cancel = GETPOST('cancel', 'aZ09');
$contextpage = GETPOST('contextpage', 'aZ') ? GETPOST('contextpage', 'aZ') : 'loanrequestcard'; // To manage different context of search
$backtopage = GETPOST('backtopage', 'alpha');
$backtopageforcancel = GETPOST('backtopageforcancel', 'alpha');

// Initialize technical objects
$object = new EmployerLoanRequest($db);
$extrafields = new ExtraFields($db);
$diroutputmassaction = $conf->employerloan->dir_output.'/temp/massgeneration/'.$user->id;
$hookmanager->initHooks(array('loanrequestcard', 'globalcard')); // Note that conf->hooks_modules contains array

// Fetch extrafields
$extrafields->fetch_name_optionals_label($object->table_element);

// Initialize array of search criterias
$search_all = GETPOST("search_all", 'alpha');
$search = array();
foreach ($object->fields as $key => $val) {
    if (GETPOST('search_'.$key, 'alpha')) {
        $search[$key] = GETPOST('search_'.$key, 'alpha');
    }
}

if (empty($action) && empty($id) && empty($ref)) {
    $action = 'view';
}

// Load object
include DOL_DOCUMENT_ROOT.'/core/actions_fetchobject.inc.php'; // Must be include, not include_once.

// Security check - Protection if external user
$socid = 0;
if ($user->socid > 0) accessforbidden();

// Security check (enable the most restrictive one)
//if ($user->rights->employerloan->myobject->read) accessforbidden();
//if (restrictedArea($user, 'employerloan', $object->id, 'employerloan_request&employerloan_request', 'myobject', 'fk_soc', 'rowid')) accessforbidden();
//if (restrictedArea($user, 'employerloan', $object->id, 'employerloan_request&employerloan_request', 'myobject')) accessforbidden();

$permissiontoread = $user->rights->employerloan->read;
$permissiontoadd = $user->rights->employerloan->write; // Used by the include of actions_addupdatedelete.inc.php and actions_lineupdown.inc.php
$permissiontodelete = $user->rights->employerloan->delete || ($permissiontoadd && isset($object->status) && $object->status == $object::STATUS_PENDING);
$permissionnote = $user->rights->employerloan->write; // Used by the include of actions_setnotes.inc.php
$permissiondellink = $user->rights->employerloan->write; // Used by the include of actions_dellink.inc.php
$upload_dir = $conf->employerloan->multidir_output[isset($object->entity) ? $object->entity : 1];

if (!$permissiontoread) accessforbidden();

/*
 * Actions
 */

$parameters = array();
$reshook = $hookmanager->executeHooks('doActions', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks
if ($reshook < 0) {
    setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
}

if (empty($reshook)) {
    $error = 0;

    $backurlforlist = dol_buildpath('/employerloan/loan_request_list.php', 1);

    if (empty($backtopage) || ($cancel && empty($backurlforlist))) {
        if (empty($backurlforlist)) {
            $backtopage = dol_buildpath('/employerloan/loan_request_list.php', 1);
        } else {
            $backtopage = $backurlforlist;
        }
    }

    $triggermodname = 'EMPLOYERLOAN_REQUEST_MODIFY'; // Name of trigger action code to execute when we modify record

    // Actions cancel, add, update, update_extras, confirm_validate, confirm_delete, confirm_deleteline, confirm_clone, confirm_close, confirm_setdraft, confirm_reopen
    include DOL_DOCUMENT_ROOT.'/core/actions_addupdatedelete.inc.php';

    // Actions when linking object to another
    include DOL_DOCUMENT_ROOT.'/core/actions_dellink.inc.php';

    // Actions when printing a doc from card
    include DOL_DOCUMENT_ROOT.'/core/actions_printing.inc.php';

    // Action to move up and down lines of object
    //include DOL_DOCUMENT_ROOT.'/core/actions_lineupdown.inc.php';

    // Action to build doc
    include DOL_DOCUMENT_ROOT.'/core/actions_builddoc.inc.php';

    if ($action == 'set_thirdparty' && $permissiontoadd) {
        $object->setValueFrom('fk_soc', GETPOST('fk_soc', 'int'), '', '', 'date', '', $user, $triggermodname);
    }
    if ($action == 'classin' && $permissiontoadd) {
        $object->setProject(GETPOST('projectid', 'int'));
    }

    // Actions to send emails
    $triggersendname = 'EMPLOYERLOAN_REQUEST_SENTBYMAIL';
    $autocopy = 'MAIN_MAIL_AUTOCOPY_EMPLOYERLOAN_REQUEST_TO';
    $trackid = 'employerloan_request'.$object->id;
    include DOL_DOCUMENT_ROOT.'/core/actions_sendmails.inc.php';

    // Action pour évaluer une demande
    if ($action == 'evaluate' && $permissiontoadd) {
        $evaluator = new EmployerLoanEvaluator($db);
        $result = $evaluator->evaluateRequest($object, $user);

        if ($result['success']) {
            setEventMessages('Évaluation effectuée avec succès. Score: '.round($result['final_score'], 2).'%', null, 'mesgs');
            $object->status = EmployerLoanRequest::STATUS_EVALUATING;
        } else {
            setEventMessages('Erreur lors de l\'évaluation: '.$result['error'], null, 'errors');
        }
    }

    // Action pour approuver une demande
    if ($action == 'approve' && $permissiontoadd) {
        $object->status = EmployerLoanRequest::STATUS_APPROVED;
        $object->date_decision = dol_now();
        $result = $object->update($user);

        if ($result > 0) {
            $object->addToHistory('request_approved', $user, 'Demande approuvée');
            setEventMessages('Demande approuvée avec succès', null, 'mesgs');
        } else {
            setEventMessages('Erreur lors de l\'approbation', null, 'errors');
        }
    }

    // Action pour rejeter une demande
    if ($action == 'reject' && $permissiontoadd) {
        $rejection_reason = GETPOST('rejection_reason', 'alpha');

        $object->status = EmployerLoanRequest::STATUS_REJECTED;
        $object->date_decision = dol_now();
        $object->rejection_reason = $rejection_reason;
        $result = $object->update($user);

        if ($result > 0) {
            $object->addToHistory('request_rejected', $user, 'Demande rejetée: '.$rejection_reason);
            setEventMessages('Demande rejetée', null, 'mesgs');
        } else {
            setEventMessages('Erreur lors du rejet', null, 'errors');
        }
    }
}

/*
 * View
 */

$form = new Form($db);
$formother = new FormOther($db);

$title = $langs->trans("LoanRequest");
$helpurl = '';
llxHeader('', $title, $helpurl);

// Example : Adding jquery code
// print '<script type="text/javascript" language="javascript">
// jQuery(document).ready(function() {
// 	function init_myfunc()
// 	{
// 		jQuery("#myid").removeAttr(\'disabled\');
// 		jQuery("#myid").attr(\'disabled\',\'disabled\');
// 	}
// 	init_myfunc();
// 	jQuery("#mybutton").click(function() {
// 		init_myfunc();
// 	});
// });
// </script>';

// Part to create
if ($action == 'create') {
    if (empty($permissiontoadd)) {
        accessforbidden($langs->trans('NotEnoughPermissions'), 0, 1);
        exit;
    }

    print load_fiche_titre($langs->trans("NewLoanRequest"), '', 'object_'.$object->picto);

    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="add">';
    if ($backtopage) {
        print '<input type="hidden" name="backtopage" value="'.$backtopage.'">';
    }
    if ($backtopageforcancel) {
        print '<input type="hidden" name="backtopageforcancel" value="'.$backtopageforcancel.'">';
    }

    print dol_get_fiche_head(array(), '');

    print '<table class="border centpercent tableforfieldcreate">'."\n";

    // Common attributes
    include DOL_DOCUMENT_ROOT.'/core/tpl/commonfields_add.tpl.php';

    // Other attributes
    include DOL_DOCUMENT_ROOT.'/core/tpl/extrafields_add.tpl.php';

    print '</table>'."\n";

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel("Create");

    print '</form>';

    //dol_set_focus('input[name="ref"]');
}

// Part to edit record
if (($id || $ref) && $action == 'edit') {
    print load_fiche_titre($langs->trans("LoanRequest"), '', 'object_'.$object->picto);

    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="update">';
    print '<input type="hidden" name="id" value="'.$object->id.'">';
    if ($backtopage) {
        print '<input type="hidden" name="backtopage" value="'.$backtopage.'">';
    }
    if ($backtopageforcancel) {
        print '<input type="hidden" name="backtopageforcancel" value="'.$backtopageforcancel.'">';
    }

    print dol_get_fiche_head(array(), '');

    print '<table class="border centpercent tableforfieldedit">'."\n";

    // Common attributes
    include DOL_DOCUMENT_ROOT.'/core/tpl/commonfields_edit.tpl.php';

    // Other attributes
    include DOL_DOCUMENT_ROOT.'/core/tpl/extrafields_edit.tpl.php';

    print '</table>';

    print dol_get_fiche_end();

    print $form->buttonsSaveCancel();

    print '</form>';
}

// Part to show record
if ($object->id > 0 && (empty($action) || ($action != 'edit' && $action != 'create'))) {
    $res = $object->fetch_optionals();

    $head = employerloan_request_prepare_head($object);
    print dol_get_fiche_head($head, 'card', $langs->trans("LoanRequest"), -1, $object->picto);

    $formconfirm = '';

    // Confirmation to delete
    if ($action == 'delete') {
        $formconfirm = $form->formconfirm($_SERVER["PHP_SELF"].'?id='.$object->id, $langs->trans('DeleteLoanRequest'), $langs->trans('ConfirmDeleteObject'), 'confirm_delete', '', 0, 1);
    }

    // Confirmation d'évaluation
    if ($action == 'evaluate') {
        $formconfirm = $form->formconfirm($_SERVER["PHP_SELF"].'?id='.$object->id, $langs->trans('EvaluateRequest'), $langs->trans('ConfirmEvaluateRequest'), 'confirm_evaluate', '', 0, 1);
    }

    // Confirmation d'approbation
    if ($action == 'approve') {
        $formconfirm = $form->formconfirm($_SERVER["PHP_SELF"].'?id='.$object->id, $langs->trans('ApproveRequest'), $langs->trans('ConfirmApproveRequest'), 'confirm_approve', '', 0, 1);
    }

    // Confirmation de rejet
    if ($action == 'reject') {
        $formconfirm = '<form action="'.$_SERVER["PHP_SELF"].'?id='.$object->id.'" method="POST">';
        $formconfirm .= '<input type="hidden" name="token" value="'.newToken().'">';
        $formconfirm .= '<input type="hidden" name="action" value="reject">';
        $formconfirm .= '<table class="noborder centpercent">';
        $formconfirm .= '<tr class="liste_titre"><td colspan="2">'.$langs->trans('RejectRequest').'</td></tr>';
        $formconfirm .= '<tr><td>'.$langs->trans('RejectionReason').'</td>';
        $formconfirm .= '<td><textarea name="rejection_reason" rows="4" cols="50" required></textarea></td></tr>';
        $formconfirm .= '</table>';
        $formconfirm .= '<div class="center">';
        $formconfirm .= '<input type="submit" class="button" value="'.$langs->trans('Reject').'">';
        $formconfirm .= ' <input type="button" class="button button-cancel" value="'.$langs->trans('Cancel').'" onclick="history.back();">';
        $formconfirm .= '</div>';
        $formconfirm .= '</form>';
    }

    // Call Hook formConfirm
    $parameters = array('formConfirm' => $formconfirm, 'lineid' => $lineid);
    $reshook = $hookmanager->executeHooks('formConfirm', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
    if (empty($reshook)) {
        $formconfirm .= $hookmanager->resPrint;
    } elseif ($reshook > 0) {
        $formconfirm = $hookmanager->resPrint;
    }

    // Print form confirm
    print $formconfirm;

    // Object card
    // ------------------------------------------------------------
    $linkback = '<a href="'.dol_buildpath('/employerloan/loan_request_list.php', 1).'?restore_lastsearch_values=1'.(!empty($socid) ? '&socid='.$socid : '').'">'.$langs->trans("BackToList").'</a>';

    $morehtmlref = '<div class="refidno">';
    $morehtmlref .= '</div>';

    dol_banner_tab($object, 'ref', $linkback, 1, 'ref', 'ref', $morehtmlref);

    print '<div class="fichecenter">';
    print '<div class="fichehalfleft">';
    print '<div class="underbanner clearboth"></div>';
    print '<table class="border centpercent tableforfield">'."\n";

    // Common attributes
    $keyforbreak = 'purpose'; // We change column just before this field
    unset($object->fields['fk_project']); // Hide field already shown in banner
    unset($object->fields['fk_soc']); // Hide field already shown in banner
    include DOL_DOCUMENT_ROOT.'/core/tpl/commonfields_view.tpl.php';

    // Other attributes. Fields from hook formObjectOptions and Extrafields.
    include DOL_DOCUMENT_ROOT.'/core/tpl/extrafields_view.tpl.php';

    print '</table>';
    print '</div>';
    print '</div>';

    print '<div class="clearboth"></div>';

    print dol_get_fiche_end();

    // Buttons for actions
    if ($action != 'presend' && $action != 'editline') {
        print '<div class="tabsAction">'."\n";
        $parameters = array();
        $reshook = $hookmanager->executeHooks('addMoreActionsButtons', $parameters, $object, $action); // Note that $action and $object may have been modified by hook
        if ($reshook < 0) {
            setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
        }

        if (empty($reshook)) {
            // Send
            if (empty($user->socid)) {
                print dolGetButtonAction('', $langs->trans('SendMail'), 'default', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=presend&mode=init&token='.newToken().'#formmailbeforetitle');
            }

            // Back to draft
            if ($object->status == $object::STATUS_EVALUATING) {
                print dolGetButtonAction('', $langs->trans('SetToDraft'), 'default', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=confirm_setdraft&confirm=yes&token='.newToken(), '', $permissiontoadd);
            }

            // Modify
            if ($permissiontoadd) {
                print dolGetButtonAction('', $langs->trans('Modify'), 'default', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=edit&token='.newToken(), '', $permissiontoadd);
            }

            // Evaluate
            if ($object->status == $object::STATUS_PENDING && $permissiontoadd) {
                print dolGetButtonAction('', $langs->trans('Evaluate'), 'default', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=evaluate&token='.newToken(), '', $permissiontoadd);
            }

            // Approve
            if ($object->status == $object::STATUS_EVALUATING && $permissiontoadd) {
                print dolGetButtonAction('', $langs->trans('Approve'), 'default', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=approve&token='.newToken(), '', $permissiontoadd);
            }

            // Reject
            if (in_array($object->status, array($object::STATUS_PENDING, $object::STATUS_EVALUATING)) && $permissiontoadd) {
                print dolGetButtonAction('', $langs->trans('Reject'), 'delete', $_SERVER["PHP_SELF"].'?id='.$object->id.'&action=reject&token='.newToken(), '', $permissiontoadd);
            }

            // Clone
            if ($permissiontoadd) {
                print dolGetButtonAction('', $langs->trans('ToClone'), 'default', $_SERVER['PHP_SELF'].'?id='.$object->id.(!empty($object->socid) ? '&socid='.$object->socid : '').'&action=clone&token='.newToken(), '', $permissiontoadd);
            }

            // Delete (need delete permission, or if draft, just need create/modify permission)
            print dolGetButtonAction('', $langs->trans('Delete'), 'delete', $_SERVER['PHP_SELF'].'?id='.$object->id.'&action=delete&token='.newToken(), '', $permissiontodelete);
        }
        print '</div>'."\n";
    }

    // Select mail models is same action as presend
    if (GETPOST('modelselected')) {
        $action = 'presend';
    }

    if ($action != 'presend') {
        print '<div class="fichecenter"><div class="fichehalfleft">';
        $MAXEVENT = 10;

        $morehtmlcenter = dolGetButtonTitle($langs->trans('SeeAll'), '', 'fa fa-bars imgforviewmode', dol_buildpath('/employerloan/loan_request_agenda.php', 1).'?id='.$object->id);

        // List of actions on element
        include_once DOL_DOCUMENT_ROOT.'/core/class/html.formactions.class.php';
        $formactions = new FormActions($db);
        $somethingshown = $formactions->showactions($object, $object->element.'@'.$object->module, (is_object($object->thirdparty) ? $object->thirdparty->id : 0), 1, '', $MAXEVENT, '', $morehtmlcenter);

        print '</div><div class="fichehalfright">';

        $MAXEVENT = 10;

        $morehtmlcenter = dolGetButtonTitle($langs->trans('SeeAll'), '', 'fa fa-bars imgforviewmode', dol_buildpath('/employerloan/loan_request_agenda.php', 1).'?id='.$object->id);

        // Show links to link elements
        $linktoelem = $form->showLinkToObjectBlock($object, null, array('employerloan_request'));
        $somethingshown = $form->showLinkedObjectBlock($object, $linktoelem);

        print '</div></div>';
    }

    //Select mail models is same action as presend
    if (GETPOST('modelselected')) {
        $action = 'presend';
    }

    // Presend form
    $modelmail = 'employerloan_request';
    $defaulttopic = 'InformationMessage';
    $diroutput = $conf->employerloan->dir_output;
    $trackid = 'employerloan_request'.$object->id;

    include DOL_DOCUMENT_ROOT.'/core/tpl/card_presend.tpl.php';
}

// End of page
llxFooter();
$db->close();