﻿<?php
require_once DOL_DOCUMENT_ROOT.'/core/class/commonobject.class.php';

class EmployerLoan extends CommonObject
{
    /**
     * @var string ID to identify managed object
     */
    public $element = 'employer_loan';

    public $table = 'employer_loan';

    /**
     * @var string Name of table without prefix where object is stored
     */
    public $table_element = 'employer_loan';

    /**
     * @var string String with name of icon for myobject. Must be the part after the 'object_' into object_myobject.png
     */
    public $picto = 'money-bill-alt';

    /**
     * @var int ID
     */
    public $rowid;
    public $entity;

    /**
     * @var integer|string date_creation
     */
    public $datec;
    public $date_creation;

    /**
     * @var integer|string date_modification
     */
    public $tms;
    public $date_modification;

    /**
     * @var integer|string date_validation
     */
    public $date_validation;

    /**
     * @var string Loan label
     */
    public $label;

    /**
     * @var int Bank ID
     */
    public $fk_bank;

    public $capital;
    public $insurance_amount;
    public $datestart;
    public $dateend;
    public $nbterm;
    public $rate;
    public $note_private;
    public $note_public;
    public $capital_position;
    public $date_position;
    public $paid;
    public $accountancy_account_capital;
    public $accountancy_account_insurance;
    public $accountancy_account_interest;

    /**
     * @var int ID
     */
    public $fk_projet;
    public $fk_project;

    /**
     * @var int ID
     */
    public $fk_user_author;
    public $fk_user_creat;

    /**
     * @var int ID
     */
    public $fk_user_modif;

    public $active;
    public $fk_employee; // Lien vers le salarié

    /**
     * @var int totalpaid
     */
    public $totalpaid; // Montant total payé

    // Status constants like standard loan module
    const STATUS_UNPAID = 0;
    const STATUS_PAID = 1;
    const STATUS_STARTED = 2;

    // Propriétés pour la compatibilité avec card.php
    public $account_capital;
    public $account_insurance;
    public $account_interest;

    public function __construct($db)
    {
        $this->db = $db;
    }

    /**
     *  Load object in memory from database
     *
     *  @param	int		$id		 id object
     *  @return int				 <0 error , >=0 no error
     */
    public function fetch($id)
    {
        $sql = "SELECT l.rowid, l.entity, l.label, l.capital, l.datestart, l.dateend, l.nbterm, l.rate, l.note_private, l.note_public, l.insurance_amount,";
        $sql .= " l.paid, l.fk_bank, l.accountancy_account_capital, l.accountancy_account_insurance, l.accountancy_account_interest, l.fk_projet as fk_project, l.fk_employee,";
        $sql .= " l.datec, l.tms, l.capital_position, l.date_position, l.fk_user_author, l.fk_user_modif, l.active";
        $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan as l";
        $sql .= " WHERE l.rowid = ".((int) $id);

        $resql = $this->db->query($sql);
        if ($resql) {
            if ($this->db->num_rows($resql)) {
                $obj = $this->db->fetch_object($resql);

                $this->rowid = $obj->rowid;
                $this->id = $obj->rowid;
                $this->entity = $obj->entity;
                $this->label = $obj->label;
                $this->capital = $obj->capital;
                $this->datestart = $this->db->jdate($obj->datestart);
                $this->dateend = $this->db->jdate($obj->dateend);
                $this->nbterm = $obj->nbterm;
                $this->rate = $obj->rate;
                $this->note_private = $obj->note_private;
                $this->note_public = $obj->note_public;
                $this->insurance_amount = $obj->insurance_amount;
                $this->paid = $obj->paid;
                $this->fk_bank = $obj->fk_bank;
                $this->accountancy_account_capital = $obj->accountancy_account_capital;
                $this->accountancy_account_insurance = $obj->accountancy_account_insurance;
                $this->accountancy_account_interest = $obj->accountancy_account_interest;
                $this->fk_project = $obj->fk_project;
                $this->fk_projet = $obj->fk_project; // For compatibility
                $this->fk_employee = $obj->fk_employee;
                $this->datec = $this->db->jdate($obj->datec);
                $this->date_creation = $this->db->jdate($obj->datec);
                $this->tms = $this->db->jdate($obj->tms);
                $this->date_modification = $this->db->jdate($obj->tms);
                $this->capital_position = $obj->capital_position;
                $this->date_position = $this->db->jdate($obj->date_position);
                $this->fk_user_author = $obj->fk_user_author;
                $this->fk_user_creat = $obj->fk_user_author; // For compatibility
                $this->fk_user_modif = $obj->fk_user_modif;
                $this->active = $obj->active;

                $this->db->free($resql);
                return 1;
            } else {
                $this->db->free($resql);
                return 0;
            }
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }

    /**
     *  Create a loan into database
     *
     *  @param	User	$user	User making creation
     *  @return int				<0 if KO, id if OK
     */
    public function create($user)
    {
        global $conf, $langs;

        $error = 0;
        $now = dol_now();

        // clean parameters
        $newcapital = price2num($this->capital, 'MT');
        if (empty($this->insurance_amount)) {
            $this->insurance_amount = 0;
        }
        $newinsuranceamount = price2num($this->insurance_amount, 'MT');
        if (isset($this->note_private)) {
            $this->note_private = trim($this->note_private);
        }
        if (isset($this->note_public)) {
            $this->note_public = trim($this->note_public);
        }
        if (isset($this->accountancy_account_capital)) {
            $this->accountancy_account_capital = trim($this->accountancy_account_capital);
        }
        if (isset($this->accountancy_account_insurance)) {
            $this->accountancy_account_insurance = trim($this->accountancy_account_insurance);
        }
        if (isset($this->accountancy_account_interest)) {
            $this->accountancy_account_interest = trim($this->accountancy_account_interest);
        }

        // Check parameters
        if (!$this->label) {
            $this->error = $langs->trans("ErrorFieldRequired", $langs->transnoentities("Label"));
            return -1;
        }
        if ($newcapital <= 0) {
            $this->error = $langs->trans("ErrorFieldRequired", $langs->transnoentities("Capital"));
            return -2;
        }

        $this->db->begin();

        $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan (label, fk_bank, capital, datestart, dateend, nbterm, rate, note_private, note_public,";
        $sql .= " accountancy_account_capital, accountancy_account_insurance, accountancy_account_interest, entity,";
        $sql .= " datec, fk_projet, fk_user_author, insurance_amount, fk_employee, active)";
        $sql .= " VALUES ('".$this->db->escape($this->label)."',";
        $sql .= " ".($this->fk_bank ? (int) $this->fk_bank : 'NULL').",";
        $sql .= " '".price2num($newcapital)."',";
        $sql .= " ".($this->datestart ? "'".$this->db->idate($this->datestart)."'" : 'NULL').",";
        $sql .= " ".($this->dateend ? "'".$this->db->idate($this->dateend)."'" : 'NULL').",";
        $sql .= " ".($this->nbterm ? "'".$this->db->escape($this->nbterm)."'" : 'NULL').",";
        $sql .= " ".($this->rate ? "'".$this->db->escape($this->rate)."'" : '0').",";
        $sql .= " '".$this->db->escape($this->note_private)."',";
        $sql .= " '".$this->db->escape($this->note_public)."',";
        $sql .= " '".$this->db->escape($this->accountancy_account_capital)."',";
        $sql .= " '".$this->db->escape($this->accountancy_account_insurance)."',";
        $sql .= " '".$this->db->escape($this->accountancy_account_interest)."',";
        $sql .= " ".((int) $conf->entity).",";
        $sql .= " '".$this->db->idate($now)."',";
        $sql .= " ".($this->fk_project ? (int) $this->fk_project : 'NULL').",";
        $sql .= " ".((int) $user->id).",";
        $sql .= " '".price2num($newinsuranceamount)."',";
        $sql .= " ".($this->fk_employee ? (int) $this->fk_employee : 'NULL').",";
        $sql .= " 1)"; // active

        $resql = $this->db->query($sql);
        if ($resql) {
            $this->rowid = $this->db->last_insert_id(MAIN_DB_PREFIX."employer_loan");
            $this->id = $this->rowid;

            // Créer automatiquement l'échéancier et les traites
            if (!$error && $this->nbterm > 0 && $this->capital > 0) {
                $monthly_capital = $this->capital / $this->nbterm;
                $monthly_interest = 0; // Pas d'intérêts pour les prêts employés
                $monthly_insurance = 0; // Pas d'assurance par défaut

                for ($i = 1; $i <= $this->nbterm; $i++) {
                    $due_date = dol_time_plus_duree($this->datestart, $i, 'm');

                    // Insérer dans employer_loan_schedule (structure réelle)
                    $sql_schedule = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_schedule";
                    $sql_schedule .= " (fk_loan, datec, datep, amount_capital, amount_insurance, amount_interest, fk_typepayment, fk_bank, fk_user_creat)";
                    $sql_schedule .= " VALUES (".$this->rowid.", '".$this->db->idate($now)."', '".$this->db->idate($due_date)."', ".$monthly_capital.", ".$monthly_insurance.", ".$monthly_interest.", 0, 0, ".$user->id.")";

                    $resql_schedule = $this->db->query($sql_schedule);
                    if ($resql_schedule) {
                        $schedule_id = $this->db->last_insert_id(MAIN_DB_PREFIX."employer_loan_schedule");

                        // Insérer dans employer_loan_installment (structure réelle)
                        $sql_installment = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_installment";
                        $sql_installment .= " (entity, fk_loan, installment_number, due_date, amount_principal, amount_interest, amount_total, status, date_creation, fk_user_creat)";
                        $sql_installment .= " VALUES (".$this->entity.", ".$this->rowid.", ".$i.", '".$this->db->idate($due_date)."', ".$monthly_capital.", ".$monthly_interest.", ".$monthly_capital.", 'pending', '".$this->db->idate($now)."', ".$user->id.")";

                        $resql_installment = $this->db->query($sql_installment);
                        if (!$resql_installment) {
                            $error++;
                            $this->error = "Error creating installment ".$i.": ".$this->db->lasterror();
                            break;
                        }
                    } else {
                        $error++;
                        $this->error = "Error creating schedule ".$i.": ".$this->db->lasterror();
                        break;
                    }
                }
            }

            if (!$error) {
                $this->db->commit();
                return $this->rowid;
            } else {
                $this->db->rollback();
                return -1;
            }
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    public function update($user)
    {
        $this->db->begin();
        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan SET ";
        $sql .= "label='".$this->db->escape($this->label)."', ";
        $sql .= "fk_bank=".((int) $this->fk_bank).", ";
        $sql .= "capital=".price2num($this->capital).", ";
        $sql .= "insurance_amount=".price2num($this->insurance_amount).", ";
        $sql .= "datestart='".$this->db->idate($this->datestart)."', ";
        $sql .= "dateend='".$this->db->idate($this->dateend)."', ";
        $sql .= "nbterm=".($this->nbterm !== null ? (float)$this->nbterm : 'NULL').", ";
        $sql .= "rate=".((float) $this->rate).", ";
        $sql .= "note_private='".$this->db->escape($this->note_private)."', ";
        $sql .= "note_public='".$this->db->escape($this->note_public)."', ";
        $sql .= "capital_position=".price2num($this->capital_position).", ";
        $sql .= "date_position='".$this->db->idate($this->date_position)."', ";
        $sql .= "paid=".((int) $this->paid).", ";
        $sql .= "accountancy_account_capital='".$this->db->escape($this->accountancy_account_capital)."', ";
        $sql .= "accountancy_account_insurance='".$this->db->escape($this->accountancy_account_insurance)."', ";
        $sql .= "accountancy_account_interest='".$this->db->escape($this->accountancy_account_interest)."', ";
        $sql .= "fk_projet=".((int) $this->fk_projet).", ";
        $sql .= "fk_user_modif=".((int) $user->id).", ";
        $sql .= "active=".((int) $this->active).", ";
        $sql .= "fk_employee=".((int) $this->fk_employee)." ";
        $sql .= "WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->db->commit();
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            $this->db->rollback();
            return -1;
        }
    }

    /**
     *  Delete a loan
     *
     *  @param	User	$user	Object user making delete
     *  @return int 			<0 if KO, >0 if OK
     */
    public function delete($user)
    {
        $error = 0;

        $this->db->begin();

        // Get bank transaction lines for this loan (if bank module is enabled)
        if (isModEnabled("banque")) {
            include_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
            $account = new Account($this->db);
            $lines_url = $account->get_url('', $this->id, 'employer_loan');

            // Delete bank urls
            foreach ($lines_url as $line_url) {
                if (!$error) {
                    include_once DOL_DOCUMENT_ROOT.'/compta/bank/class/account.class.php';
                    $accountline = new AccountLine($this->db);
                    $accountline->fetch($line_url['fk_bank']);
                    $result = $accountline->delete_urls($user);
                    if ($result < 0) {
                        if (!is_array($this->errors)) $this->errors = array();
                        $this->errors = array_merge($this->errors, [$accountline->error], $accountline->errors);
                        $error++;
                    }
                }
            }

            // Delete bank transaction lines
            foreach ($lines_url as $line_url) {
                if (!$error) {
                    $accountline = new AccountLine($this->db);
                    $accountline->fetch($line_url['fk_bank']);
                    $result = $accountline->delete($user);
                    if ($result < 0) {
                        if (!is_array($this->errors)) $this->errors = array();
                        $this->errors = array_merge($this->errors, [$accountline->error], $accountline->errors);
                        $error++;
                    }
                }
            }
        }

        if (!$error) {
            $sql = "DELETE FROM ".MAIN_DB_PREFIX."employer_payment_loan WHERE fk_loan = ".((int) $this->id);
            $resql = $this->db->query($sql);
            if (!$resql) {
                $this->error = $this->db->lasterror();
                $error++;
            }
        }

        if (!$error) {
            $sql = "DELETE FROM ".MAIN_DB_PREFIX."employer_loan_schedule WHERE fk_loan = ".((int) $this->id);
            $resql = $this->db->query($sql);
            if (!$resql) {
                $this->error = $this->db->lasterror();
                $error++;
            }
        }

        if (!$error) {
            $sql = "DELETE FROM ".MAIN_DB_PREFIX."employer_loan WHERE rowid = ".((int) $this->id);
            $resql = $this->db->query($sql);
            if (!$resql) {
                $this->error = $this->db->lasterror();
                $error++;
            }
        }

        if (!$error) {
            $this->db->commit();
            return 1;
        } else {
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Return a link to the object card (with optionaly the picto)
     *
     * @param int $withpicto Include picto in link (0=No picto, 1=Include picto into link, 2=Only picto)
     * @param string $option On what the link point to ('nolink', ...)
     * @param int $notooltip 1=Disable tooltip
     * @param string $morecss Add more css on link
     * @param int $save_lastsearch_value -1=Auto, 0=No save of lastsearch_values when clicking, 1=Save lastsearch_values whenclicking
     * @return string HTML
     */
    public function getNomUrl($withpicto = 0, $option = '', $notooltip = 0, $morecss = '', $save_lastsearch_value = -1)
    {
        global $conf, $langs, $hookmanager;

        if (!empty($conf->dol_no_mouse_hover)) {
            $notooltip = 1; // Force disable tooltips
        }

        $result = '';

        $label = img_picto('', $this->picto).' <u>'.$langs->trans("EmployerLoan").'</u>';
        if (isset($this->paid)) {
            $label .= ' '.$this->getLibStatut(5);
        }
        $label .= '<br>';
        $label .= '<b>'.$langs->trans('Ref').':</b> '.$this->rowid.'<br>';
        $label .= '<b>'.$langs->trans('Label').':</b> '.$this->label.'<br>';
        $label .= '<b>'.$langs->trans('Amount').':</b> '.price($this->capital).'<br>';

        $url = DOL_URL_ROOT.'/custom/employerloan/card.php?id='.($this->id ? $this->id : $this->rowid);

        if ($option != 'nolink') {
            // Add param to save lastsearch_values or not
            $add_save_lastsearch_values = ($save_lastsearch_value == 1 ? 1 : 0);
            if ($save_lastsearch_value == -1 && preg_match('/list\.php/', $_SERVER["PHP_SELF"])) {
                $add_save_lastsearch_values = 1;
            }
            if ($add_save_lastsearch_values) {
                $url .= '&save_lastsearch_values=1';
            }
        }

        $linkclose = '';
        if (empty($notooltip)) {
            if (!empty($conf->global->MAIN_OPTIMIZEFORTEXTBROWSER)) {
                $label = $langs->trans("ShowEmployerLoan");
                $linkclose .= ' alt="'.dol_escape_htmltag($label, 1).'"';
            }
            $linkclose .= ' title="'.dol_escape_htmltag($label, 1).'"';
            $linkclose .= ' class="classfortooltip'.($morecss ? ' '.$morecss : '').'"';
        } else {
            $linkclose = ($morecss ? ' class="'.$morecss.'"' : '');
        }

        if ($option == 'nolink') {
            $linkstart = '<span';
        } else {
            $linkstart = '<a href="'.$url.'"';
        }
        $linkstart .= $linkclose.'>';
        if ($option == 'nolink') {
            $linkend = '</span>';
        } else {
            $linkend = '</a>';
        }

        $result .= $linkstart;

        if ($withpicto) {
            $result .= img_object(($notooltip ? '' : $label), ($this->picto ? $this->picto : 'generic'), ($notooltip ? (($withpicto != 2) ? 'class="paddingright"' : '') : 'class="'.(($withpicto != 2) ? 'paddingright ' : '').'classfortooltip"'), 0, 0, $notooltip ? 0 : 1);
        }

        if ($withpicto != 2) {
            $result .= $this->rowid;
        }

        $result .= $linkend;

        global $action, $hookmanager;
        $hookmanager->initHooks(array('employerloandao'));
        $parameters = array('id'=>$this->rowid, 'getnomurl'=>$result);
        $reshook = $hookmanager->executeHooks('getNomUrl', $parameters, $this, $action); // Note that $action and $object may have been modified by some hooks
        if ($reshook > 0) {
            $result = $hookmanager->resPrint;
        } else {
            $result .= $hookmanager->resPrint;
        }

        return $result;
    }

    /**
     *  Calculate amount (capital+interests) not yet paid
     *
     *  @return     int         <0 if KO, amount if OK
     */
    public function getSumPayment()
    {
        $table = 'employer_payment_loan';
        $field = 'amount_capital';

        $sql = 'SELECT sum('.$field.') as amount';
        $sql .= ' FROM '.MAIN_DB_PREFIX.$table;
        $sql .= ' WHERE fk_loan = '.((int) $this->id);

        dol_syslog(get_class($this)."::getSumPayment", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $amount = 0;

            $obj = $this->db->fetch_object($resql);
            if ($obj) {
                $amount = $obj->amount ? $obj->amount : 0;
            }

            $this->db->free($resql);
            return $amount;
        } else {
            $this->error = $this->db->error();
            return -1;
        }
    }

    /**
     *  Return amount of loan paid
     *
     *  @return     int         <0 if KO, amount if OK
     */
    public function getSumPaymentCapital()
    {
        return $this->getSumPayment();
    }

    /**
     *  Return amount (interests) of loan paid
     *
     *  @return     int         <0 if KO, amount if OK
     */
    public function getSumPaymentInterest()
    {
        $table = 'employer_payment_loan';
        $field = 'amount_interest';

        $sql = 'SELECT sum('.$field.') as amount';
        $sql .= ' FROM '.MAIN_DB_PREFIX.$table;
        $sql .= ' WHERE fk_loan = '.((int) $this->id);

        dol_syslog(get_class($this)."::getSumPaymentInterest", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $amount = 0;

            $obj = $this->db->fetch_object($resql);
            if ($obj) {
                $amount = $obj->amount ? $obj->amount : 0;
            }

            $this->db->free($resql);
            return $amount;
        } else {
            $this->error = $this->db->error();
            return -1;
        }
    }

    /**
     *  Return amount (insurance) of loan paid
     *
     *  @return     int         <0 if KO, amount if OK
     */
    public function getSumPaymentInsurance()
    {
        $table = 'employer_payment_loan';
        $field = 'amount_insurance';

        $sql = 'SELECT sum('.$field.') as amount';
        $sql .= ' FROM '.MAIN_DB_PREFIX.$table;
        $sql .= ' WHERE fk_loan = '.((int) $this->id);

        dol_syslog(get_class($this)."::getSumPaymentInsurance", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $amount = 0;

            $obj = $this->db->fetch_object($resql);
            if ($obj) {
                $amount = $obj->amount ? $obj->amount : 0;
            }

            $this->db->free($resql);
            return $amount;
        } else {
            $this->error = $this->db->error();
            return -1;
        }
    }

    /**
     * Return the status
     *
     * @param int $status Id status
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @param int $alreadypaid Amount already paid
     * @return string Label of status
     */
    public function LibStatut($status, $mode = 0, $alreadypaid = 0)
    {
        return $this->getLibStatut($mode, $alreadypaid);
    }

    /**
     * Return the status
     *
     * @param int $mode 0=long label, 1=short label, 2=Picto + short label, 3=Picto, 4=Picto + long label, 5=Short label + Picto, 6=Long label + Picto
     * @param int $alreadypaid Amount already paid
     * @return string Label of status
     */
    public function getLibStatut($mode = 0, $alreadypaid = 0)
    {
        global $langs;

        $statusType = 'status4';
        if ($this->paid == 1) {
            $statusType = 'status6';
        }

        $labelStatus = $langs->trans('Unpaid');
        $labelStatusShort = $langs->trans('Unpaid');
        if ($this->paid == 1) {
            $labelStatus = $langs->trans('Paid');
            $labelStatusShort = $langs->trans('Paid');
        }

        return dolGetStatus($labelStatus, $labelStatusShort, '', $statusType, $mode);
    }

    /**
     * Return clicable name (with picto eventually)
     *
     * @param string $option Where point the link ('card', 'nolink', ...)
     * @param array $arraydata Array of data
     * @return string HTML code
     */
    public function getKanbanView($option = '', $arraydata = null)
    {
        global $conf, $langs;

        $selected = (empty($arraydata['selected']) ? 0 : $arraydata['selected']);

        $return = '<div class="box-flex-item box-flex-grow-zero">';
        $return .= '<div class="info-box info-box-sm">';
        $return .= '<span class="info-box-icon bg-infobox-action">';
        $return .= img_picto('', $this->picto);
        $return .= '</span>';
        $return .= '<div class="info-box-content">';
        $return .= '<span class="info-box-ref inline-block tdoverflowmax150 valignmiddle">'.(method_exists($this, 'getNomUrl') ? $this->getNomUrl() : $this->rowid).'</span>';
        if ($selected >= 0) {
            $return .= '<input id="cb'.$this->rowid.'" class="flat checkforselect fright" type="checkbox" name="toselect[]" value="'.$this->rowid.'"'.($selected ? ' checked="checked"' : '').'>';
        }
        if (property_exists($this, 'label')) {
            $return .= ' <div class="inline-block opacitymedium valignmiddle tdoverflowmax100">'.$this->label.'</div>';
        }
        if (property_exists($this, 'capital')) {
            $return .= '<br><span class="info-box-label amount">'.price($this->capital).'</span>';
        }
        if (method_exists($this, 'getLibStatut')) {
            $return .= '<br><div class="info-box-status">'.$this->getLibStatut(3).'</div>';
        }
        $return .= '</div>';
        $return .= '</div>';
        $return .= '</div>';

        return $return;
    }

    /**
     * Set loan as paid
     *
     * @param User $user User making the action
     * @return int <0 if KO, >0 if OK
     */
    public function setPaid($user)
    {
        $this->db->begin();

        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan SET paid = 1 WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);

        if ($resql) {
            $this->paid = 1;
            $this->db->commit();
            return 1;
        } else {
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Set loan as unpaid
     *
     * @param User $user User making the action
     * @return int <0 if KO, >0 if OK
     */
    public function setUnpaid($user)
    {
        $this->db->begin();

        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan SET paid = 0 WHERE rowid = ".((int) $this->rowid);
        $resql = $this->db->query($sql);

        if ($resql) {
            $this->paid = 0;
            $this->db->commit();
            return 1;
        } else {
            $this->db->rollback();
            return -1;
        }
    }

    /**
     * Return amount of payments already done
     *
     * @return float Amount of payment already done, <0 if KO
     */
    public function getSommePaiement()
    {
        return $this->getSumPayment();
    }



    /**
     *  Set project
     *
     *  @param	int		$projectid		Project ID
     *  @return int						<0 if KO, >0 if OK
     */
    public function setProject($projectid, $notrigger = 0)
    {
        $sql = "UPDATE ".MAIN_DB_PREFIX."employer_loan SET fk_projet = ".($projectid > 0 ? (int) $projectid : 'NULL')." WHERE rowid = ".((int) $this->id);

        dol_syslog(get_class($this)."::setProject", LOG_DEBUG);
        $resql = $this->db->query($sql);
        if ($resql) {
            $this->fk_project = $projectid;
            $this->fk_projet = $projectid;
            return 1;
        } else {
            $this->error = $this->db->lasterror();
            return -1;
        }
    }
}