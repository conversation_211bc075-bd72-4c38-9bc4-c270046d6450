<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * \file    loan_request_committee.php
 * \ingroup employerloan
 * \brief   Page d'évaluation d'une demande de prêt par le comité
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
    $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
    $i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
    $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
    $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) {
    $res = @include "../main.inc.php";
}
if (!$res && file_exists("../../main.inc.php")) {
    $res = @include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
    $res = @include "../../../main.inc.php";
}
if (!$res) {
    die("Include of main fails");
}

require_once DOL_DOCUMENT_ROOT.'/core/class/html.formfile.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_request.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_committee.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_criteria.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/lib/employerloan.lib.php';

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "other"));

// Get parameters
$id = GETPOST('id', 'int');
$ref = GETPOST('ref', 'alpha');
$action = GETPOST('action', 'aZ09');
$confirm = GETPOST('confirm', 'alpha');
$cancel = GETPOST('cancel', 'aZ09');
$contextpage = GETPOST('contextpage', 'aZ') ? GETPOST('contextpage', 'aZ') : 'loanrequestcommittee';
$backtopage = GETPOST('backtopage', 'alpha');

// Initialize technical objects
$object = new EmployerLoanRequest($db);
$extrafields = new ExtraFields($db);
$diroutputmassaction = $conf->employerloan->dir_output.'/temp/massgeneration/'.$user->id;
$hookmanager->initHooks(array('loanrequestcommittee', 'globalcard')); // Note that conf->hooks_modules contains array

// Fetch optionals attributes and labels
$extrafields->fetch_name_optionals_label($object->table_element);

$search_array_options = $extrafields->getOptionalsFromPost($object->table_element, '', 'search_');

// Initialize array of search criterias
$search_all = GETPOST("search_all", 'alpha');
$search = array();
foreach ($object->fields as $key => $val) {
    if (GETPOST('search_'.$key, 'alpha')) {
        $search[$key] = GETPOST('search_'.$key, 'alpha');
    }
}

if (empty($action) && empty($id) && empty($ref)) {
    $action = 'view';
}

// Load object
include DOL_DOCUMENT_ROOT.'/core/actions_fetchobject.inc.php'; // Must be include, not include_once.

// Security check - Protection if external user
$socid = 0;
if ($user->socid > 0) {
    accessforbidden();
}

$permissiontoread = $user->rights->employerloan->read;
$permissiontoadd = $user->rights->employerloan->write; // Used by the include of actions_addupdatedelete.inc.php and actions_lineupdown.inc.php
$permissiontodelete = $user->rights->employerloan->delete || ($permissiontoadd && isset($object->status) && $object->status == EmployerLoanRequest::STATUS_PENDING);
$permissionnote = $user->rights->employerloan->write; // Used by the include of actions_setnotes.inc.php
$permissiondellink = $user->rights->employerloan->write; // Used by the include of actions_dellink.inc.php
$upload_dir = $conf->employerloan->multidir_output[isset($object->entity) ? $object->entity : 1];

// Security check (enable the most restrictive one)
if ($user->socid > 0) accessforbidden();
if (!$permissiontoread) accessforbidden();

/*
 * Actions
 */

$parameters = array();
$reshook = $hookmanager->executeHooks('doActions', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks
if ($reshook < 0) {
    setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
}

if (empty($reshook)) {
    $error = 0;

    $backurlforlist = dol_buildpath('/employerloan/loan_request_list.php', 1);

    if (empty($backtopage) || ($cancel && empty($backtopage))) {
        if (empty($backtopage) || ($cancel && strpos($backtopage, '__ID__'))) {
            if (empty($id) && (($action != 'add' && $action != 'create') || $cancel)) {
                $backtopage = $backurlforlist;
            } else {
                $backtopage = dol_buildpath('/employerloan/loan_request_card.php', 1).'?id='.((!empty($id) && $id > 0) ? $id : '__ID__');
            }
        }
    }

    // Action pour évaluer la demande
    if ($action == 'evaluate') {
        $evaluation_comments = GETPOST('evaluation_comments', 'alpha');
        $decision = GETPOST('decision', 'alpha');
        $rejection_reason = GETPOST('rejection_reason', 'alpha');

        // Récupérer les scores des critères
        $criteria_scores = array();
        $total_score = 0;
        $criteria_count = 0;
        $final_score = 0;

        // Charger les critères actifs
        $criteria = new EmployerLoanCriteria($db);
        $criteria_list = $criteria->fetchAll('ASC', 'position', 0, 0, array('active' => 1));

        if (is_array($criteria_list) && count($criteria_list) > 0) {
            // Évaluation avec critères
            $total_weight = 0;
            foreach ($criteria_list as $criterion) {
                $score = GETPOST('criteria_'.$criterion->rowid, 'alpha');
                if (!empty($score)) {
                    $weight = !empty($criterion->weight) ? $criterion->weight : 10;
                    $criteria_scores[$criterion->rowid] = floatval($score);
                    $total_score += floatval($score) * $weight;
                    $total_weight += $weight;
                    $criteria_count++;
                }
            }

            // Calculer le score moyen pondéré
            $final_score = $total_weight > 0 ? $total_score / $total_weight : 0;
        } else {
            // Fallback: score global si pas de critères
            $global_score = GETPOST('global_score', 'alpha');
            $final_score = !empty($global_score) ? floatval($global_score) : 0;
        }

        if ($final_score > 0 && !empty($decision)) {
            $object->evaluation_score = $final_score;
            $object->evaluation_comments = $evaluation_comments;
            $object->date_decision = dol_now();

            // Sauvegarder les scores des critères (optionnel - peut être ajouté plus tard)
            // TODO: Créer une table pour stocker les scores détaillés par critère

            if ($decision == 'approve') {
                $object->status = EmployerLoanRequest::STATUS_APPROVED;
            } elseif ($decision == 'reject') {
                $object->status = EmployerLoanRequest::STATUS_REJECTED;
                $object->rejection_reason = $rejection_reason;
            }

            $result = $object->update($user);
            if ($result > 0) {
                // Insérer la décision du comité (structure réelle)
                $sql_decision = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_committee_decision";
                $sql_decision .= " (entity, fk_loan_request, fk_user, decision, comments, date_decision)";
                $sql_decision .= " VALUES (".$conf->entity.", ".$object->id.", ".$user->id.", '".$decision."', '".$db->escape($evaluation_comments)."', NOW())";
                $sql_decision .= " ON DUPLICATE KEY UPDATE decision = '".$decision."', comments = '".$db->escape($evaluation_comments)."', date_decision = NOW()";
                $result_decision = $db->query($sql_decision);

                // Insérer les évaluations détaillées par critère (structure réelle)
                if (is_array($criteria_list) && count($criteria_list) > 0) {
                    foreach ($criteria_list as $criterion) {
                        $score = GETPOST('criteria_'.$criterion->rowid, 'alpha');
                        if (!empty($score)) {
                            $sql_eval = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_evaluation";
                            $sql_eval .= " (entity, fk_loan_request, fk_criteria, value_obtained, score, comments, date_evaluation, fk_user_eval, fk_user)";
                            $sql_eval .= " VALUES (".$conf->entity.", ".$object->id.", ".$criterion->rowid.", ".$score.", ".$score.", '".$db->escape($evaluation_comments)."', NOW(), ".$user->id.", ".$user->id.")";
                            $sql_eval .= " ON DUPLICATE KEY UPDATE value_obtained = ".$score.", score = ".$score.", comments = '".$db->escape($evaluation_comments)."', date_evaluation = NOW()";
                            $result_eval = $db->query($sql_eval);
                        }
                    }
                }

                setEventMessages($langs->trans("RequestEvaluated"), null, 'mesgs');
                header("Location: ".$_SERVER['PHP_SELF'].'?id='.$object->id);
                exit;
            } else {
                setEventMessages($object->error, $object->errors, 'errors');
            }
        } else {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("CriteriaScores")), null, 'errors');
        }
    }
}

/*
 * View
 */

$form = new Form($db);
$formfile = new FormFile($db);

$title = $langs->trans("LoanRequestCommitteeEvaluation");
$help_url = '';
llxHeader('', $title, $help_url);

// Part to show record
if ($object->id > 0 && (empty($action) || ($action != 'edit' && $action != 'create'))) {
    $res = $object->fetch_optionals();

    $head = employerloan_request_prepare_head($object);
    print dol_get_fiche_head($head, 'committee', $langs->trans("LoanRequest"), -1, $object->picto);

    $linkback = '<a href="'.dol_buildpath('/employerloan/loan_request_list.php', 1).'?restore_lastsearch_values=1'.(!empty($socid) ? '&socid='.$socid : '').'">'.$langs->trans("BackToList").'</a>';

    $morehtmlref = '<div class="refidno">';
    $morehtmlref .= '</div>';

    dol_banner_tab($object, 'ref', $linkback, 1, 'ref', 'ref', $morehtmlref);

    print '<div class="fichecenter">';
    print '<div class="fichehalfleft">';
    print '<div class="underbanner clearboth"></div>';
    print '<table class="border centpercent tableforfield">'."\n";

    // Informations de base
    print '<tr><td class="titlefield">'.$langs->trans("Employee").'</td><td>';
    $employee = new User($db);
    $employee->fetch($object->fk_employee);
    print $employee->getNomUrl(1);
    print '</td></tr>';

    print '<tr><td>'.$langs->trans("AmountRequested").'</td><td>'.price($object->amount_requested).'</td></tr>';
    print '<tr><td>'.$langs->trans("Purpose").'</td><td>'.$object->purpose.'</td></tr>';
    print '<tr><td>'.$langs->trans("DurationMonths").'</td><td>'.$object->duration_months.' '.$langs->trans("months").'</td></tr>';
    print '<tr><td>'.$langs->trans("DateRequest").'</td><td>'.dol_print_date($object->date_request, 'day').'</td></tr>';

    print '</table>';
    print '</div>';

    print '<div class="fichehalfright">';
    print '<div class="underbanner clearboth"></div>';
    print '<table class="border centpercent tableforfield">'."\n";

    // Informations financières
    if ($object->monthly_salary > 0) {
        print '<tr><td class="titlefield">'.$langs->trans("MonthlySalary").'</td><td>'.price($object->monthly_salary).'</td></tr>';
    }
    if ($object->other_loans > 0) {
        print '<tr><td>'.$langs->trans("OtherLoans").'</td><td>'.price($object->other_loans).'</td></tr>';
    }

    // Informations garant
    if (!empty($object->guarantor_name)) {
        print '<tr><td>'.$langs->trans("GuarantorName").'</td><td>'.$object->guarantor_name.'</td></tr>';
        if (!empty($object->guarantor_phone)) {
            print '<tr><td>'.$langs->trans("GuarantorPhone").'</td><td>'.$object->guarantor_phone.'</td></tr>';
        }
    }

    // Statut actuel
    print '<tr><td>'.$langs->trans("Status").'</td><td>'.$object->getLibStatut(4).'</td></tr>';

    print '</table>';
    print '</div>';
    print '</div>';

    print '<div class="clearboth"></div>';

    print dol_get_fiche_end();

    // Formulaire d'évaluation
    if ($object->status == EmployerLoanRequest::STATUS_PENDING || $object->status == EmployerLoanRequest::STATUS_EVALUATING) {
        print '<div class="tabsAction">';
        print '<div class="inline-block divButAction"><a class="butAction" href="#evaluation">'.$langs->trans("EvaluateRequest").'</a></div>';
        print '</div>';

        print '<a name="evaluation"></a>';
        print '<div class="div-table-responsive-no-min">';
        print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'?id='.$object->id.'">';
        print '<input type="hidden" name="token" value="'.newToken().'">';
        print '<input type="hidden" name="action" value="evaluate">';

        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th colspan="3">'.$langs->trans("CommitteeEvaluation").'</th>';
        print '</tr>';

        // Afficher les critères d'évaluation
        $criteria = new EmployerLoanCriteria($db);
        $criteria_list = $criteria->fetchAll('ASC', 'position', 0, 0, array('active' => 1));

        if (is_array($criteria_list) && count($criteria_list) > 0) {
            print '<tr class="liste_titre">';
            print '<th>'.$langs->trans("EvaluationCriteria").'</th>';
            print '<th class="center" width="100">'.$langs->trans("Weight").'</th>';
            print '<th class="center" width="150">'.$langs->trans("Score").' (0-10)</th>';
            print '</tr>';

            foreach ($criteria_list as $criterion) {
                print '<tr>';
                print '<td>';
                print '<strong>'.$criterion->label.'</strong>';
                if (!empty($criterion->description)) {
                    print '<br><small class="opacitymedium">'.$criterion->description.'</small>';
                }
                print '</td>';
                print '<td class="center">';
                print !empty($criterion->weight) ? $criterion->weight.'%' : '10%';
                print '</td>';
                print '<td class="center">';
                print '<input type="number" name="criteria_'.$criterion->rowid.'" min="0" max="10" step="0.1" ';
                print 'value="'.GETPOST('criteria_'.$criterion->rowid, 'alpha').'" required ';
                print 'style="width: 80px; text-align: center;">';
                print '</td>';
                print '</tr>';
            }

            print '<tr><td colspan="3"><hr></td></tr>';
        } else {
            // Si pas de critères définis, afficher un message
            print '<tr><td colspan="3" class="opacitymedium center">';
            print $langs->trans("NoCriteriaConfigured").'<br>';
            print '<a href="'.DOL_URL_ROOT.'/custom/employerloan/admin/employerloan.php?mainmenu=home&leftmenu=employerloan_setup">';
            print $langs->trans("ConfigureCriteria").'</a>';
            print '</td></tr>';

            // Fallback: score global
            print '<tr><td class="titlefield fieldrequired">'.$langs->trans("EvaluationScore").' (0-10)</td>';
            print '<td colspan="2"><input type="number" name="global_score" min="0" max="10" step="0.1" value="'.GETPOST('global_score', 'alpha').'" required></td></tr>';
        }

        // Commentaires
        print '<tr><td class="titlefield">'.$langs->trans("EvaluationComments").'</td>';
        print '<td colspan="2"><textarea name="evaluation_comments" rows="4" cols="80">'.GETPOST('evaluation_comments', 'alpha').'</textarea></td></tr>';

        // Décision
        print '<tr><td class="titlefield fieldrequired">'.$langs->trans("Decision").'</td>';
        print '<td>';
        print '<input type="radio" name="decision" value="approve" id="approve" required> <label for="approve">'.$langs->trans("Approve").'</label><br>';
        print '<input type="radio" name="decision" value="reject" id="reject" required> <label for="reject">'.$langs->trans("Reject").'</label>';
        print '</td></tr>';

        // Motif de rejet (conditionnel)
        print '<tr id="rejection_reason_row" style="display:none;"><td class="titlefield">'.$langs->trans("RejectionReason").'</td>';
        print '<td><textarea name="rejection_reason" rows="3" cols="80">'.GETPOST('rejection_reason', 'alpha').'</textarea></td></tr>';

        print '</table>';

        print '<div class="center">';
        print '<input type="submit" class="button" value="'.$langs->trans("SubmitEvaluation").'">';
        print ' &nbsp; ';
        print '<input type="button" class="button button-cancel" value="'.$langs->trans("Cancel").'" onclick="history.back()">';
        print '</div>';

        print '</form>';
        print '</div>';

        // JavaScript pour afficher/masquer le motif de rejet
        print '<script type="text/javascript">
        jQuery(document).ready(function() {
            jQuery("input[name=decision]").change(function() {
                if (jQuery(this).val() == "reject") {
                    jQuery("#rejection_reason_row").show();
                } else {
                    jQuery("#rejection_reason_row").hide();
                }
            });
        });
        </script>';

    } else {
        // Afficher les résultats de l'évaluation si déjà évaluée
        if ($object->evaluation_score > 0 || !empty($object->evaluation_comments)) {
            print '<div class="div-table-responsive-no-min">';
            print '<table class="noborder centpercent">';
            print '<tr class="liste_titre">';
            print '<th colspan="2">'.$langs->trans("EvaluationResults").'</th>';
            print '</tr>';

            if ($object->evaluation_score > 0) {
                print '<tr><td class="titlefield">'.$langs->trans("EvaluationScore").'</td>';
                print '<td><strong>'.$object->evaluation_score.'/10</strong></td></tr>';
            }

            if (!empty($object->evaluation_comments)) {
                print '<tr><td class="titlefield">'.$langs->trans("EvaluationComments").'</td>';
                print '<td>'.nl2br($object->evaluation_comments).'</td></tr>';
            }

            if ($object->date_decision) {
                print '<tr><td class="titlefield">'.$langs->trans("DateDecision").'</td>';
                print '<td>'.dol_print_date($object->date_decision, 'dayhour').'</td></tr>';
            }

            if ($object->status == EmployerLoanRequest::STATUS_REJECTED && !empty($object->rejection_reason)) {
                print '<tr><td class="titlefield">'.$langs->trans("RejectionReason").'</td>';
                print '<td>'.nl2br($object->rejection_reason).'</td></tr>';
            }

            print '</table>';
            print '</div>';
        }
    }
}

// End of page
llxFooter();
