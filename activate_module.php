<?php
/* Copyright (C) 2024 NextGestion
 *
 * Script d'activation manuelle pour le module EmployerLoan
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

// Security check
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Activation Module EmployerLoan</title></head><body>';
print '<h1>🚀 Activation Manuelle Module EmployerLoan</h1>';

$action = GETPOST('action', 'alpha');

if ($action == 'activate') {
    print '<h2>Tentative d\'activation...</h2>';
    
    $db->begin();
    
    try {
        // 1. Charger la classe du module
        $moduleFile = DOL_DOCUMENT_ROOT.'/custom/employerloan/core/modules/modEmployerLoan.class.php';
        include_once $moduleFile;
        
        if (!class_exists('modEmployerLoan')) {
            throw new Exception('Classe modEmployerLoan non trouvée');
        }
        
        $module = new modEmployerLoan($db);
        print '✅ Module instancié<br>';
        
        // 2. Exécuter les scripts SQL manuellement
        print '<h3>Installation des tables...</h3>';
        
        // Charger le fichier SQL principal
        $sqlfile = DOL_DOCUMENT_ROOT.'/custom/employerloan/sql/employerloan_tables.sql';
        if (file_exists($sqlfile)) {
            $sqlcontent = file_get_contents($sqlfile);
            $sqlcommands = explode(';', $sqlcontent);
            
            foreach ($sqlcommands as $sqlcommand) {
                $sqlcommand = trim($sqlcommand);
                if (!empty($sqlcommand) && !preg_match('/^--/', $sqlcommand)) {
                    print 'Exécution : '.substr($sqlcommand, 0, 50).'...<br>';
                    $resql = $db->query($sqlcommand);
                    if (!$resql) {
                        print '❌ Erreur SQL : '.$db->lasterror().'<br>';
                    } else {
                        print '✅ OK<br>';
                    }
                }
            }
        }
        
        // Charger le fichier SQL des améliorations
        $sqlfile2 = DOL_DOCUMENT_ROOT.'/custom/employerloan/sql/employerloan_ameliorations.sql';
        if (file_exists($sqlfile2)) {
            $sqlcontent2 = file_get_contents($sqlfile2);
            $sqlcommands2 = explode(';', $sqlcontent2);
            
            foreach ($sqlcommands2 as $sqlcommand2) {
                $sqlcommand2 = trim($sqlcommand2);
                if (!empty($sqlcommand2) && !preg_match('/^--/', $sqlcommand2)) {
                    print 'Exécution : '.substr($sqlcommand2, 0, 50).'...<br>';
                    $resql = $db->query($sqlcommand2);
                    if (!$resql) {
                        print '❌ Erreur SQL : '.$db->lasterror().'<br>';
                    } else {
                        print '✅ OK<br>';
                    }
                }
            }
        }
        
        // 3. Activer le module via la méthode standard
        print '<h3>Activation du module...</h3>';
        $result = $module->init();
        
        if ($result > 0) {
            print '✅ Module activé avec succès !<br>';
            
            // 4. Vérifier l'activation
            print '<h3>Vérification...</h3>';
            
            // Vérifier la constante
            $sql = "SELECT value FROM ".MAIN_DB_PREFIX."const WHERE name = 'MAIN_MODULE_EMPLOYERLOAN'";
            $resql = $db->query($sql);
            if ($resql && $db->num_rows($resql)) {
                $obj = $db->fetch_object($resql);
                print '✅ Constante créée : MAIN_MODULE_EMPLOYERLOAN = '.$obj->value.'<br>';
            } else {
                print '❌ Constante non créée<br>';
            }
            
            // Vérifier les droits
            $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."rights_def WHERE module = 'employerloan'";
            $resql = $db->query($sql);
            if ($resql) {
                $obj = $db->fetch_object($resql);
                print '✅ '.$obj->nb.' droits créés<br>';
            }
            
            // Vérifier les menus
            $sql = "SELECT COUNT(*) as nb FROM ".MAIN_DB_PREFIX."menu WHERE module = 'employerloan'";
            $resql = $db->query($sql);
            if ($resql) {
                $obj = $db->fetch_object($resql);
                print '✅ '.$obj->nb.' menus créés<br>';
            }
            
            // Vérifier les tables
            $tables = array('employer_loan', 'employer_loan_request', 'employer_loan_criteria', 'employer_loan_evaluation', 'employer_loan_committee', 'employer_loan_installment', 'employer_loan_history');
            $tablesOK = 0;
            foreach ($tables as $table) {
                $sql = "SHOW TABLES LIKE '".MAIN_DB_PREFIX.$table."'";
                $resql = $db->query($sql);
                if ($resql && $db->num_rows($resql) > 0) {
                    $tablesOK++;
                }
            }
            print '✅ '.$tablesOK.'/'.count($tables).' tables créées<br>';
            
            $db->commit();
            
            print '<div style="background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;">';
            print '<h3 style="color: #155724;">🎉 Activation Réussie !</h3>';
            print '<p>Le module EmployerLoan a été activé avec succès.</p>';
            print '<p><strong>Prochaines étapes :</strong></p>';
            print '<ul>';
            print '<li>Aller dans <a href="/admin/perms.php">Configuration > Utilisateurs & Groupes</a> pour configurer les permissions</li>';
            print '<li>Accéder au module via <a href="/custom/employerloan/list.php">HRM > Crédits salariés</a></li>';
            print '<li>Configurer les critères d\'évaluation dans <a href="/custom/employerloan/admin/employerloan.php">Configuration</a></li>';
            print '</ul>';
            print '</div>';
            
        } else {
            throw new Exception('Échec de l\'activation (code: '.$result.')');
        }
        
    } catch (Exception $e) {
        $db->rollback();
        print '<div style="background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 20px 0;">';
        print '<h3 style="color: #721c24;">❌ Erreur d\'Activation</h3>';
        print '<p>Erreur : '.$e->getMessage().'</p>';
        print '<p>Vérifiez les logs pour plus de détails.</p>';
        print '</div>';
    }
    
} else {
    // Afficher le formulaire d'activation
    print '<h2>Prêt pour l\'activation</h2>';
    
    // Vérifications préalables
    print '<h3>Vérifications préalables :</h3>';
    
    // Vérifier la classe
    $moduleFile = DOL_DOCUMENT_ROOT.'/custom/employerloan/core/modules/modEmployerLoan.class.php';
    if (file_exists($moduleFile)) {
        print '✅ Fichier de classe trouvé<br>';
        
        include_once $moduleFile;
        if (class_exists('modEmployerLoan')) {
            print '✅ Classe modEmployerLoan disponible<br>';
        } else {
            print '❌ Classe modEmployerLoan non trouvée<br>';
        }
    } else {
        print '❌ Fichier de classe manquant<br>';
    }
    
    // Vérifier les fichiers SQL
    $sqlfile = DOL_DOCUMENT_ROOT.'/custom/employerloan/sql/employerloan_tables.sql';
    if (file_exists($sqlfile)) {
        print '✅ Fichier SQL principal trouvé<br>';
    } else {
        print '❌ Fichier SQL principal manquant<br>';
    }
    
    $sqlfile2 = DOL_DOCUMENT_ROOT.'/custom/employerloan/sql/employerloan_ameliorations.sql';
    if (file_exists($sqlfile2)) {
        print '✅ Fichier SQL améliorations trouvé<br>';
    } else {
        print '❌ Fichier SQL améliorations manquant<br>';
    }
    
    // Vérifier les permissions
    if (is_writable(DOL_DATA_ROOT)) {
        print '✅ Permissions d\'écriture OK<br>';
    } else {
        print '❌ Permissions d\'écriture insuffisantes<br>';
    }
    
    // État actuel du module
    if (isset($conf->employerloan) && $conf->employerloan->enabled) {
        print '<div style="background-color: #d1ecf1; padding: 15px; border: 1px solid #bee5eb; border-radius: 5px; margin: 20px 0;">';
        print '<h3 style="color: #0c5460;">ℹ️ Module Déjà Activé</h3>';
        print '<p>Le module EmployerLoan est déjà activé.</p>';
        print '</div>';
    } else {
        print '<div style="background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px 0;">';
        print '<h3 style="color: #856404;">⚠️ Module Non Activé</h3>';
        print '<p>Le module EmployerLoan n\'est pas encore activé.</p>';
        print '<p><strong>Cette activation va :</strong></p>';
        print '<ul>';
        print '<li>Créer toutes les tables de base de données</li>';
        print '<li>Installer les permissions et menus</li>';
        print '<li>Configurer le module</li>';
        print '</ul>';
        print '<p><a href="'.$_SERVER['PHP_SELF'].'?action=activate" class="button" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🚀 Activer le Module</a></p>';
        print '</div>';
    }
}

print '<br><br>';
print '<a href="/admin/modules.php" style="background-color: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">📋 Retour aux Modules</a> ';
print '<a href="/custom/employerloan/diagnostic_module.php" style="background-color: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔧 Diagnostic</a>';

print '</body></html>';
