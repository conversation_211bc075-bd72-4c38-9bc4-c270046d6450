<?php
/**
 * Page À propos du module EmployerLoan
 * Design moderne et professionnel
 */

// Ne pas inclure main.inc.php ici car ce fichier est inclus dans employerloan.php
// require '../main.inc.php';

// Chargement des traductions
$langs->load('employerloan@employerloan');

// Le contrôle de sécurité est fait dans le fichier parent

// Pas de llxHeader() car c'est un contenu d'onglet

// CSS personnalisé pour un design moderne
print '<style>
.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.about-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.about-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
    font-weight: 300;
}

.about-header p {
    margin: 0;
    font-size: 1.2em;
    opacity: 0.9;
}

.about-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.about-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e1e8ed;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.about-card h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.4em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.about-card ul {
    list-style: none;
    padding: 0;
}

.about-card li {
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
    position: relative;
    padding-left: 25px;
}

.about-card li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

.about-card li:last-child {
    border-bottom: none;
}

.info-table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.info-table table {
    width: 100%;
    border-collapse: collapse;
}

.info-table td {
    padding: 15px 20px;
    border-bottom: 1px solid #ecf0f1;
}

.info-table td:first-child {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    width: 200px;
}

.info-table tr:last-child td {
    border-bottom: none;
}

.logos-section {
    text-align: center;
    background: #f8f9fa;
    padding: 40px;
    border-radius: 15px;
    margin-top: 30px;
}

.logos-section h3 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 1.5em;
}

.logos-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    flex-wrap: wrap;
}

.logo-item {
    transition: transform 0.3s ease;
}

.logo-item:hover {
    transform: scale(1.05);
}

.logo-item img {
    max-height: 80px;
    max-width: 200px;
    border-radius: 10px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .about-cards {
        grid-template-columns: 1fr;
    }

    .logos-container {
        flex-direction: column;
        gap: 20px;
    }

    .about-header h1 {
        font-size: 2em;
    }
}
</style>';

print '<div class="about-container">';

// En-tête moderne
print '<div class="about-header">';
print '<h1><i class="fas fa-hand-holding-usd"></i> Prêt salarié</h1>';
print '<p>Système complet de gestion des prêts employés pour Dolibarr</p>';
print '</div>';

// Informations du module
print '<div class="info-table">';
print '<table>';
print '<tr><td><i class="fas fa-tag"></i> '.$langs->trans('ModuleName').'</td><td><strong>Prêt salarié - Gestion des prêts employés</strong></td></tr>';
print '<tr><td><i class="fas fa-code-branch"></i> '.$langs->trans('Version').'</td><td><strong>2.0.0</strong> <span style="color: #27ae60;">(Dernière version)</span></td></tr>';
print '<tr><td><i class="fas fa-user-tie"></i> '.$langs->trans('Author').'</td><td><strong>Équipe de développement</strong></td></tr>';
print '<tr><td><i class="fas fa-balance-scale"></i> '.$langs->trans('License').'</td><td><strong>GPL v3+</strong> - Licence libre</td></tr>';
print '<tr><td><i class="fas fa-calendar-alt"></i> Date de création</td><td><strong>'.date('Y').'</strong></td></tr>';
print '<tr><td><i class="fas fa-globe"></i> Compatibilité</td><td><strong>Dolibarr 18.0+</strong></td></tr>';
print '</table>';
print '</div>';

// Cartes de fonctionnalités
print '<div class="about-cards">';

// Fonctionnalités principales
print '<div class="about-card">';
print '<h3><i class="fas fa-star"></i> Fonctionnalités Principales</h3>';
print '<ul>';
print '<li>Gestion complète des demandes de prêt</li>';
print '<li>Workflow d\'approbation avec comité</li>';
print '<li>Calcul automatique des échéances</li>';
print '<li>Suivi des paiements et remboursements</li>';
print '<li>Intégration comptable complète</li>';
print '<li>Génération de contrats PDF</li>';
print '<li>Historique détaillé des transactions</li>';
print '</ul>';
print '</div>';

// Avantages
print '<div class="about-card">';
print '<h3><i class="fas fa-thumbs-up"></i> Avantages</h3>';
print '<ul>';
print '<li>Interface intuitive et moderne</li>';
print '<li>Respect des normes comptables</li>';
print '<li>Workflow professionnel</li>';
print '<li>Sécurité et traçabilité</li>';
print '<li>Intégration native Dolibarr</li>';
print '<li>Support multi-devises</li>';
print '<li>Rapports et statistiques</li>';
print '</ul>';
print '</div>';

// Support technique
print '<div class="about-card">';
print '<h3><i class="fas fa-life-ring"></i> Support & Maintenance</h3>';
print '<ul>';
print '<li>Documentation complète</li>';
print '<li>Mises à jour régulières</li>';
print '<li>Support technique disponible</li>';
print '<li>Formation utilisateurs</li>';
print '<li>Personnalisations possibles</li>';
print '<li>Migration de données</li>';
print '<li>Sauvegarde et restauration</li>';
print '</ul>';
print '</div>';

print '</div>'; // Fin about-cards

// Section description détaillée
print '<div class="about-card" style="margin-bottom: 30px;">';
print '<h3><i class="fas fa-info-circle"></i> Description du Module</h3>';
print '<p style="line-height: 1.6; color: #555; font-size: 1.1em;">';
print 'Le module <strong>Prêt salarié</strong> est une solution complète pour la gestion des prêts accordés par l\'employeur à ses salariés. ';
print 'Il offre un workflow professionnel depuis la demande initiale jusqu\'au remboursement final, avec une intégration comptable native dans Dolibarr. ';
print 'Le système inclut un processus d\'évaluation par comité, la génération automatique de contrats, le suivi des échéances et la gestion des paiements.';
print '</p>';
print '<p style="line-height: 1.6; color: #555; font-size: 1.1em; margin-top: 15px;">';
print 'Conçu pour respecter les réglementations en vigueur, le module assure une traçabilité complète des opérations et facilite ';
print 'la gestion administrative des prêts employés dans votre organisation.';
print '</p>';
print '</div>';

// Section logos et partenariats
print '<div class="logos-section">';
print '<h3><i class="fas fa-handshake"></i> Partenaires & Technologies</h3>';
print '<div class="logos-container">';

// Logo du module
if (file_exists(DOL_DOCUMENT_ROOT.'/custom/employerloan/img/prêt salarié.png')) {
    print '<div class="logo-item">';
    print '<img src="'.DOL_URL_ROOT.'/custom/employerloan/img/prêt salarié.png" alt="Module Prêt salarié" title="Module Prêt salarié" />';
    print '<p style="margin-top: 10px; font-weight: 600; color: #2c3e50;">Module Prêt salarié</p>';
    print '</div>';
}

// Logo Dolibarr Partner
if (file_exists(DOL_DOCUMENT_ROOT.'/custom/employerloan/img/Dolibarr_Preferred_Partner_logo.png')) {
    print '<div class="logo-item">';
    print '<img src="'.DOL_URL_ROOT.'/custom/employerloan/img/Dolibarr_Preferred_Partner_logo.png" alt="Dolibarr Preferred Partner" title="Partenaire Préféré Dolibarr" />';
    print '<p style="margin-top: 10px; font-weight: 600; color: #2c3e50;">Partenaire Dolibarr</p>';
    print '</div>';
}

// Logo MIT (si disponible)
if (file_exists(DOL_DOCUMENT_ROOT.'/custom/employerloan/img/MIT_logo.jpg')) {
    print '<div class="logo-item">';
    print '<img src="'.DOL_URL_ROOT.'/custom/employerloan/img/MIT_logo.jpg" alt="MIT License" title="Licence MIT" />';
    print '<p style="margin-top: 10px; font-weight: 600; color: #2c3e50;">Licence MIT</p>';
    print '</div>';
}

print '</div>'; // Fin logos-container
print '</div>'; // Fin logos-section

// Section statistiques/métriques
print '<div class="about-cards" style="margin-top: 30px;">';

print '<div class="about-card">';
print '<h3><i class="fas fa-chart-line"></i> Métriques du Module</h3>';
print '<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; text-align: center;">';
print '<div style="padding: 20px; background: #e8f5e8; border-radius: 10px;">';
print '<div style="font-size: 2em; font-weight: bold; color: #27ae60;">100%</div>';
print '<div style="color: #555;">Fonctionnel</div>';
print '</div>';
print '<div style="padding: 20px; background: #e8f4fd; border-radius: 10px;">';
print '<div style="font-size: 2em; font-weight: bold; color: #3498db;">5+</div>';
print '<div style="color: #555;">Tables DB</div>';
print '</div>';
print '<div style="padding: 20px; background: #fef9e7; border-radius: 10px;">';
print '<div style="font-size: 2em; font-weight: bold; color: #f39c12;">15+</div>';
print '<div style="color: #555;">Pages</div>';
print '</div>';
print '<div style="padding: 20px; background: #fdebf7; border-radius: 10px;">';
print '<div style="font-size: 2em; font-weight: bold; color: #e91e63;">2.0</div>';
print '<div style="color: #555;">Version</div>';
print '</div>';
print '</div>';
print '</div>';

print '<div class="about-card">';
print '<h3><i class="fas fa-cogs"></i> Technologies Utilisées</h3>';
print '<div style="display: flex; flex-wrap: wrap; gap: 10px;">';
$technologies = ['PHP 7.4+', 'MySQL/MariaDB', 'HTML5/CSS3', 'JavaScript', 'Bootstrap', 'Dolibarr API', 'PDF Generation', 'TCPDF'];
foreach ($technologies as $tech) {
    print '<span style="background: #f1f2f6; padding: 8px 15px; border-radius: 20px; font-size: 0.9em; color: #2c3e50; border: 1px solid #ddd;">'.$tech.'</span>';
}
print '</div>';
print '</div>';

print '</div>'; // Fin about-cards

// Footer avec informations de contact/support
print '<div style="text-align: center; margin-top: 40px; padding: 30px; background: #2c3e50; color: white; border-radius: 15px;">';
print '<h3 style="margin-bottom: 20px;"><i class="fas fa-envelope"></i> Support & Contact</h3>';
print '<p style="margin-bottom: 15px; font-size: 1.1em;">Pour toute question, support technique ou demande de personnalisation :</p>';
print '<div style="display: flex; justify-content: center; gap: 30px; flex-wrap: wrap; margin-top: 20px;">';
print '<div><i class="fas fa-book"></i> <strong>Documentation :</strong> Incluse dans le module</div>';
print '<div><i class="fas fa-bug"></i> <strong>Bugs :</strong> Système de tickets intégré</div>';
print '<div><i class="fas fa-star"></i> <strong>Évaluation :</strong> Module testé et validé</div>';
print '</div>';
print '</div>';

print '</div>'; // Fin about-container

// Pas de llxFooter() car c'est un contenu d'onglet