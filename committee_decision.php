<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * \file    committee_decision.php
 * \ingroup employerloan
 * \brief   Committee decision page for loan requests
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

require_once DOL_DOCUMENT_ROOT.'/core/class/html.form.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/user/class/user.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_request.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_committee.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_criteria.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan.class.php';

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "other"));

// Get parameters
$id = GETPOST('id', 'int');
$action = GETPOST('action', 'aZ09');
$confirm = GETPOST('confirm', 'alpha');

// Security check
if (!$user->hasRight('employerloan', 'write')) {
    accessforbidden();
}

// Initialize technical objects
$object = new EmployerLoanRequest($db);
$committee = new EmployerLoanCommittee($db);

// Load object
if ($id > 0) {
    $result = $object->fetch($id);
    if ($result <= 0) {
        dol_print_error($db, $object->error);
        exit;
    }
}

/*
 * Actions
 */

if ($action == 'approve' && $confirm == 'yes') {
    $db->begin();
    
    $error = 0;
    
    // Update request status
    $object->status = 2; // Approved
    $object->date_decision = dol_now();
    $result = $object->update($user);
    
    if ($result > 0) {
        // Create the loan automatically
        $employee = new User($db);
        $employee->fetch($object->fk_employee);

        $loan = new EmployerLoan($db);
        $loan->label = 'Prêt pour '.$employee->getFullName($langs).' - '.$object->purpose;
        $loan->capital = $object->amount_requested;
        $loan->datestart = dol_now();
        $loan->dateend = dol_time_plus_duree(dol_now(), $object->duration_months, 'm');
        $loan->nbterm = $object->duration_months;
        $loan->rate = 0; // No interest for employee loans
        $loan->fk_employee = $object->fk_employee;
        $loan->paid = 0;
        $loan->entity = $conf->entity;
        
        $result = $loan->create($user);
        if ($result > 0) {
            // Generate installments
            $monthly_amount = $object->amount_requested / $object->duration_months;
            
            for ($i = 1; $i <= $object->duration_months; $i++) {
                $installment_date = dol_time_plus_duree(dol_now(), $i, 'm');
                
                $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_installment";
                $sql .= " (entity, fk_loan, installment_number, due_date, amount_principal, amount_interest, amount_total, status, date_creation, fk_user_creat)";
                $sql .= " VALUES (".$conf->entity.", ".$loan->id.", ".$i.", '".$db->idate($installment_date)."', ".$monthly_amount.", 0, ".$monthly_amount.", 'pending', '".$db->idate(dol_now())."', ".$user->id.")";
                
                $resql = $db->query($sql);
                if (!$resql) {
                    $error++;
                    break;
                }
            }
            
            if (!$error) {
                setEventMessages($langs->trans("LoanRequestApproved"), null, 'mesgs');
                $db->commit();
            } else {
                setEventMessages($langs->trans("ErrorCreatingInstallments"), null, 'errors');
                $db->rollback();
            }
        } else {
            setEventMessages($langs->trans("ErrorCreatingLoan"), null, 'errors');
            $db->rollback();
        }
    } else {
        setEventMessages($object->error, $object->errors, 'errors');
        $db->rollback();
    }
}

if ($action == 'reject' && $confirm == 'yes') {
    $object->status = 3; // Rejected
    $object->date_decision = dol_now();
    $object->rejection_reason = GETPOST('rejection_reason', 'alpha');
    
    $result = $object->update($user);
    if ($result > 0) {
        setEventMessages($langs->trans("LoanRequestRejected"), null, 'mesgs');
    } else {
        setEventMessages($object->error, $object->errors, 'errors');
    }
}

if ($action == 'vote') {
    $vote = GETPOST('vote', 'alpha');
    $comments = GETPOST('comments', 'alpha');
    
    // Record committee member vote
    $sql = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_committee_decision";
    $sql .= " (entity, fk_loan_request, fk_user, decision, comments, date_decision)";
    $sql .= " VALUES (".$conf->entity.", ".$object->id.", ".$user->id.", '".$db->escape($vote)."', '".$db->escape($comments)."', '".$db->idate(dol_now())."')";
    $sql .= " ON DUPLICATE KEY UPDATE decision = '".$db->escape($vote)."', comments = '".$db->escape($comments)."', date_decision = '".$db->idate(dol_now())."'";
    
    $resql = $db->query($sql);
    if ($resql) {
        setEventMessages($langs->trans("VoteRecorded"), null, 'mesgs');
    } else {
        setEventMessages($langs->trans("ErrorRecordingVote"), null, 'errors');
    }
}

// Action pour évaluer la demande avec critères
if ($action == 'evaluate') {
    $evaluation_comments = GETPOST('evaluation_comments', 'alpha');
    $decision = GETPOST('decision', 'alpha');
    $rejection_reason = GETPOST('rejection_reason', 'alpha');

    // Récupérer les scores des critères
    $criteria_scores = array();
    $total_score = 0;
    $criteria_count = 0;
    $final_score = 0;

    // Charger les critères actifs
    $criteria = new EmployerLoanCriteria($db);
    $criteria_list = $criteria->fetchAll('ASC', 'position', 0, 0, array('active' => 1));

    if (is_array($criteria_list) && count($criteria_list) > 0) {
        // Évaluation avec critères
        $total_weight = 0;
        foreach ($criteria_list as $criterion) {
            $score = GETPOST('criteria_'.$criterion->rowid, 'alpha');
            if (!empty($score)) {
                $weight = !empty($criterion->weight) ? $criterion->weight : 10;
                $criteria_scores[$criterion->rowid] = floatval($score);
                $total_score += floatval($score) * $weight;
                $total_weight += $weight;
                $criteria_count++;
            }
        }

        // Calculer le score moyen pondéré
        $final_score = $total_weight > 0 ? $total_score / $total_weight : 0;
    } else {
        // Fallback: score global si pas de critères
        $global_score = GETPOST('global_score', 'alpha');
        $final_score = !empty($global_score) ? floatval($global_score) : 0;
    }

    if ($final_score > 0 && !empty($decision)) {
        $object->evaluation_score = $final_score;
        $object->evaluation_comments = $evaluation_comments;
        $object->date_decision = dol_now();

        // Sauvegarder les scores des critères (optionnel - peut être ajouté plus tard)
        // TODO: Créer une table pour stocker les scores détaillés par critère

        if ($decision == 'approve') {
            $object->status = EmployerLoanRequest::STATUS_APPROVED;
        } elseif ($decision == 'reject') {
            $object->status = EmployerLoanRequest::STATUS_REJECTED;
            $object->rejection_reason = $rejection_reason;
        }

        $result = $object->update($user);
        if ($result > 0) {
            // Insérer la décision du comité (structure réelle)
            $sql_decision = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_committee_decision";
            $sql_decision .= " (entity, fk_loan_request, fk_user, decision, comments, date_decision)";
            $sql_decision .= " VALUES (".$conf->entity.", ".$object->id.", ".$user->id.", '".$decision."', '".$db->escape($evaluation_comments)."', NOW())";
            $sql_decision .= " ON DUPLICATE KEY UPDATE decision = '".$decision."', comments = '".$db->escape($evaluation_comments)."', date_decision = NOW()";
            $result_decision = $db->query($sql_decision);

            if ($result_decision) {
                print "✅ Décision du comité insérée<br>";
            } else {
                print "❌ Erreur insertion décision : ".$db->lasterror()."<br>";
            }

            // Insérer les évaluations détaillées par critère (structure réelle)
            if (is_array($criteria_list) && count($criteria_list) > 0) {
                foreach ($criteria_list as $criterion) {
                    $score = GETPOST('criteria_'.$criterion->rowid, 'alpha');
                    if (!empty($score)) {
                        $sql_eval = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_evaluation";
                        $sql_eval .= " (entity, fk_loan_request, fk_criteria, value_obtained, score, comments, date_evaluation, fk_user_eval, fk_user)";
                        $sql_eval .= " VALUES (".$conf->entity.", ".$object->id.", ".$criterion->rowid.", ".$score.", ".$score.", '".$db->escape($evaluation_comments)."', NOW(), ".$user->id.", ".$user->id.")";
                        $sql_eval .= " ON DUPLICATE KEY UPDATE value_obtained = ".$score.", score = ".$score.", comments = '".$db->escape($evaluation_comments)."', date_evaluation = NOW()";
                        $result_eval = $db->query($sql_eval);

                        if ($result_eval) {
                            print "✅ Évaluation critère ".$criterion->rowid." insérée<br>";
                        } else {
                            print "❌ Erreur insertion évaluation : ".$db->lasterror()."<br>";
                        }
                    }
                }
            }

            setEventMessages($langs->trans("RequestEvaluated"), null, 'mesgs');

            // Si approuvé, créer automatiquement le prêt complet
            if ($decision == 'approve') {
                $employee = new User($db);
                $employee->fetch($object->fk_employee);

                // 1. Créer le prêt principal
                $loan = new EmployerLoan($db);
                $loan->label = 'Prêt pour '.$employee->getFullName($langs).' - '.$object->purpose;
                $loan->capital = $object->amount_requested;
                $loan->datestart = dol_now();
                $loan->dateend = dol_time_plus_duree(dol_now(), $object->duration_months, 'm');
                $loan->nbterm = $object->duration_months;
                $loan->rate = 0; // Pas d'intérêts pour les prêts employés
                $loan->fk_employee = $object->fk_employee;
                $loan->paid = 0;
                $loan->entity = $conf->entity;

                // Utiliser les rowid spécifiques des comptes comptables
                $loan->accountancy_account_capital = '*********';    // Compte comptable capital
                $loan->accountancy_account_interest = '*********';   // Compte comptable intérêts
                $loan->accountancy_account_insurance = '*********';  // Compte comptable assurance

                $loan_result = $loan->create($user);
                if ($loan_result > 0) {
                    // 2. L'échéancier et les traites sont créés automatiquement par la méthode create() de la classe
                    // Pas besoin de code supplémentaire ici

                    // 3. Insérer la décision du comité
                    $sql_decision = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_committee_decision";
                    $sql_decision .= " (fk_loan_request, fk_user, decision, comments, evaluation_score, date_decision, date_creation, fk_user_creat)";
                    $sql_decision .= " VALUES (".$object->id.", ".$user->id.", '".$decision."', '".$db->escape($evaluation_comments)."', ".$final_score.", NOW(), NOW(), ".$user->id.")";
                    $db->query($sql_decision);

                    // 4. Insérer les évaluations détaillées par critère
                    if (is_array($criteria_list) && count($criteria_list) > 0) {
                        foreach ($criteria_list as $criterion) {
                            $score = GETPOST('criteria_'.$criterion->rowid, 'alpha');
                            if (!empty($score)) {
                                $sql_eval = "INSERT INTO ".MAIN_DB_PREFIX."employer_loan_evaluation";
                                $sql_eval .= " (fk_loan_request, fk_criteria, fk_user, score, comments, date_evaluation, date_creation, fk_user_creat)";
                                $sql_eval .= " VALUES (".$object->id.", ".$criterion->rowid.", ".$user->id.", ".$score.", '".$db->escape($evaluation_comments)."', NOW(), NOW(), ".$user->id.")";
                                $db->query($sql_eval);
                            }
                        }
                    }

                    setEventMessages($langs->trans("LoanAndScheduleCreated")." (".$object->duration_months." échéances et traites)", null, 'mesgs');
                } else {
                    setEventMessages($langs->trans("ErrorCreatingLoan"), null, 'errors');
                }
            }

            // Rediriger vers la liste
            header("Location: ".$_SERVER['PHP_SELF']);
            exit;
        } else {
            setEventMessages($object->error, $object->errors, 'errors');
        }
    } else {
        if ($final_score <= 0) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("CriteriaScores")), null, 'errors');
        }
        if (empty($decision)) {
            setEventMessages($langs->trans("ErrorFieldRequired", $langs->transnoentitiesnoconv("Decision")), null, 'errors');
        }
    }
}

/*
 * View
 */

$form = new Form($db);
$formother = new FormOther($db);

$title = $langs->trans('CommitteeDecision');
$help_url = '';

llxHeader('', $title, $help_url);

// Check if user is committee member
$sql = "SELECT rowid, role, max_amount FROM ".MAIN_DB_PREFIX."employer_loan_committee";
$sql .= " WHERE fk_user = ".$user->id." AND active = 1 AND entity = ".$conf->entity;
$resql = $db->query($sql);
$is_committee_member = false;
$user_role = '';
$max_amount = 0;

if ($resql && $db->num_rows($resql) > 0) {
    $obj = $db->fetch_object($resql);
    $is_committee_member = true;
    $user_role = $obj->role;
    $max_amount = $obj->max_amount;
}

if (!$is_committee_member) {
    print '<div class="error">'.$langs->trans("YouAreNotCommitteeMember").'</div>';
    llxFooter();
    exit;
}

// Si pas d'ID spécifique, afficher la liste des demandes à évaluer
if (empty($id)) {
    print load_fiche_titre($langs->trans('LoanRequestsToEvaluate'), '', 'object_loan');

    // Filtres
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';

    print '<div class="div-table-responsive-no-min">';
    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<th>'.$langs->trans("Ref").'</th>';
    print '<th>'.$langs->trans("Employee").'</th>';
    print '<th class="right">'.$langs->trans("AmountRequested").'</th>';
    print '<th>'.$langs->trans("Purpose").'</th>';
    print '<th class="center">'.$langs->trans("DurationMonths").'</th>';
    print '<th class="center">'.$langs->trans("DateRequest").'</th>';
    print '<th class="center">'.$langs->trans("Status").'</th>';
    print '<th class="center">'.$langs->trans("Actions").'</th>';
    print '</tr>';

    // Récupérer les demandes en attente d'évaluation
    $sql = "SELECT r.rowid, r.ref, r.fk_employee, r.amount_requested, r.purpose, r.duration_months,";
    $sql .= " r.date_request, r.status, u.lastname, u.firstname";
    $sql .= " FROM ".MAIN_DB_PREFIX."employer_loan_request as r";
    $sql .= " LEFT JOIN ".MAIN_DB_PREFIX."user as u ON r.fk_employee = u.rowid";
    $sql .= " WHERE r.entity = ".$conf->entity;
    $sql .= " AND r.status IN (".EmployerLoanRequest::STATUS_PENDING.", ".EmployerLoanRequest::STATUS_EVALUATING.")";

    // Filtrer selon les droits du membre du comité
    if ($max_amount > 0) {
        $sql .= " AND r.amount_requested <= ".$max_amount;
    }

    $sql .= " ORDER BY r.date_request ASC";

    $resql = $db->query($sql);
    if ($resql) {
        $num = $db->num_rows($resql);

        if ($num > 0) {
            $i = 0;
            while ($i < $num) {
                $obj = $db->fetch_object($resql);

                $request_static = new EmployerLoanRequest($db);
                $request_static->id = $obj->rowid;
                $request_static->ref = $obj->ref;
                $request_static->status = $obj->status;

                $employee_static = new User($db);
                $employee_static->id = $obj->fk_employee;
                $employee_static->lastname = $obj->lastname;
                $employee_static->firstname = $obj->firstname;

                print '<tr class="oddeven">';

                // Ref
                print '<td>';
                print $request_static->getNomUrl(1);
                print '</td>';

                // Employee
                print '<td>';
                print $employee_static->getNomUrl(1);
                print '</td>';

                // Amount
                print '<td class="right">';
                print price($obj->amount_requested);
                print '</td>';

                // Purpose
                print '<td>';
                print dol_trunc($obj->purpose, 40);
                print '</td>';

                // Duration
                print '<td class="center">';
                print $obj->duration_months.' '.$langs->trans("months");
                print '</td>';

                // Date request
                print '<td class="center">';
                print dol_print_date($db->jdate($obj->date_request), 'day');
                print '</td>';

                // Status
                print '<td class="center">';
                print $request_static->getLibStatut(3);
                print '</td>';

                // Actions
                print '<td class="center">';
                print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'?id='.$obj->rowid.'">';
                print $langs->trans("Evaluate");
                print '</a>';
                print '</td>';

                print '</tr>';
                $i++;
            }
        } else {
            print '<tr><td colspan="8" class="opacitymedium center">';
            print $langs->trans("NoRequestsToEvaluate");
            print '</td></tr>';
        }

        $db->free($resql);
    } else {
        dol_print_error($db);
    }

    print '</table>';
    print '</div>';
    print '</form>';

    llxFooter();
    exit;
}

// Évaluation d'une demande spécifique
print load_fiche_titre($langs->trans('EvaluateLoanRequest').' - '.$object->ref, '', 'object_loan');

// Lien de retour vers la liste
print '<div class="tabsAction">';
print '<a class="butAction" href="'.$_SERVER["PHP_SELF"].'">'.$langs->trans("BackToList").'</a>';
print '</div>';

// Load employee info
$employee = new User($db);
$employee->fetch($object->fk_employee);

// Informations de la demande
print '<div class="fichecenter">';
print '<div class="fichehalfleft">';
print '<div class="underbanner clearboth"></div>';

print '<table class="border centpercent tableforfield">';

// Employee
print '<tr><td class="titlefield">'.$langs->trans('Employee').'</td>';
print '<td>'.$employee->getNomUrl(1).'</td></tr>';

// Amount requested
print '<tr><td>'.$langs->trans('AmountRequested').'</td>';
print '<td class="amount">'.price($object->amount_requested).'</td></tr>';

// Purpose
print '<tr><td>'.$langs->trans('Purpose').'</td>';
print '<td>'.$object->purpose.'</td></tr>';

// Duration
print '<tr><td>'.$langs->trans('DurationMonths').'</td>';
print '<td>'.$object->duration_months.' '.$langs->trans('months').'</td></tr>';

// Date request
print '<tr><td>'.$langs->trans('DateRequest').'</td>';
print '<td>'.dol_print_date($object->date_request, 'day').'</td></tr>';

print '</table>';
print '</div>';

print '<div class="fichehalfright">';
print '<div class="underbanner clearboth"></div>';
print '<table class="border centpercent tableforfield">';

// Monthly salary
if ($object->monthly_salary > 0) {
    print '<tr><td class="titlefield">'.$langs->trans('MonthlySalary').'</td>';
    print '<td>'.price($object->monthly_salary).'</td></tr>';
}

// Other loans
if ($object->other_loans > 0) {
    print '<tr><td>'.$langs->trans('OtherLoans').'</td>';
    print '<td>'.price($object->other_loans).'</td></tr>';
}

// Guarantor info
if (!empty($object->guarantor_name)) {
    print '<tr><td>'.$langs->trans('GuarantorName').'</td>';
    print '<td>'.$object->guarantor_name.'</td></tr>';

    if (!empty($object->guarantor_phone)) {
        print '<tr><td>'.$langs->trans('GuarantorPhone').'</td>';
        print '<td>'.$object->guarantor_phone.'</td></tr>';
    }
}

// Current status
print '<tr><td>'.$langs->trans('Status').'</td>';
print '<td>'.$object->getLibStatut(4).'</td></tr>';

print '</table>';
print '</div>';
print '</div>';

print '<div class="clearboth"></div>';
// Formulaire d'évaluation avec critères
if ($object->status == EmployerLoanRequest::STATUS_PENDING || $object->status == EmployerLoanRequest::STATUS_EVALUATING) {
    print '<div class="div-table-responsive-no-min">';
    print '<form method="POST" action="'.$_SERVER["PHP_SELF"].'?id='.$object->id.'">';
    print '<input type="hidden" name="token" value="'.newToken().'">';
    print '<input type="hidden" name="action" value="evaluate">';

    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<th colspan="3">'.$langs->trans("CommitteeEvaluation").'</th>';
    print '</tr>';

    // Afficher les critères d'évaluation
    $criteria = new EmployerLoanCriteria($db);
    $criteria_list = $criteria->fetchAll('ASC', 'position', 0, 0, array('active' => 1));

    if (is_array($criteria_list) && count($criteria_list) > 0) {
        print '<tr class="liste_titre">';
        print '<th>'.$langs->trans("EvaluationCriteria").'</th>';
        print '<th class="center" width="100">'.$langs->trans("Weight").'</th>';
        print '<th class="center" width="150">'.$langs->trans("Score").' (0-10)</th>';
        print '</tr>';

        foreach ($criteria_list as $criterion) {
            print '<tr>';
            print '<td>';
            print '<strong>'.$criterion->label.'</strong>';
            if (!empty($criterion->description)) {
                print '<br><small class="opacitymedium">'.$criterion->description.'</small>';
            }
            print '</td>';
            print '<td class="center">';
            print !empty($criterion->weight) ? $criterion->weight.'%' : '10%';
            print '</td>';
            print '<td class="center">';
            print '<input type="number" name="criteria_'.$criterion->rowid.'" min="0" max="10" step="0.1" ';
            print 'required style="width: 80px; text-align: center;">';
            print '</td>';
            print '</tr>';
        }

        print '<tr><td colspan="3"><hr></td></tr>';
    } else {
        // Fallback: score global
        print '<tr><td class="titlefield fieldrequired">'.$langs->trans("EvaluationScore").' (0-10)</td>';
        print '<td colspan="2"><input type="number" name="global_score" min="0" max="10" step="0.1" required></td></tr>';
    }

    // Commentaires
    print '<tr><td class="titlefield">'.$langs->trans("EvaluationComments").'</td>';
    print '<td colspan="2"><textarea name="evaluation_comments" rows="4" cols="80"></textarea></td></tr>';

    // Décision
    print '<tr><td class="titlefield fieldrequired">'.$langs->trans("Decision").'</td>';
    print '<td colspan="2">';
    print '<input type="radio" name="decision" value="approve" id="approve" required> <label for="approve">'.$langs->trans("Approve").'</label><br>';
    print '<input type="radio" name="decision" value="reject" id="reject" required> <label for="reject">'.$langs->trans("Reject").'</label>';
    print '</td></tr>';

    // Motif de rejet (conditionnel)
    print '<tr id="rejection_reason_row" style="display:none;"><td class="titlefield">'.$langs->trans("RejectionReason").'</td>';
    print '<td colspan="2"><textarea name="rejection_reason" rows="3" cols="80"></textarea></td></tr>';

    print '</table>';

    print '<div class="center">';
    print '<input type="submit" class="button" value="'.$langs->trans("SubmitEvaluation").'">';
    print ' &nbsp; ';
    print '<input type="button" class="button button-cancel" value="'.$langs->trans("Cancel").'" onclick="history.back()">';
    print '</div>';

    print '</form>';
    print '</div>';

    // JavaScript pour afficher/masquer le motif de rejet
    print '<script type="text/javascript">
    jQuery(document).ready(function() {
        jQuery("input[name=decision]").change(function() {
            if (jQuery(this).val() == "reject") {
                jQuery("#rejection_reason_row").show();
            } else {
                jQuery("#rejection_reason_row").hide();
            }
        });
    });
    </script>';

} else {
    // Afficher les résultats de l'évaluation si déjà évaluée
    print '<div class="div-table-responsive-no-min">';
    print '<table class="noborder centpercent">';
    print '<tr class="liste_titre">';
    print '<th colspan="2">'.$langs->trans("EvaluationResults").'</th>';
    print '</tr>';

    if ($object->evaluation_score > 0) {
        print '<tr><td class="titlefield">'.$langs->trans("EvaluationScore").'</td>';
        print '<td><strong>'.$object->evaluation_score.'/10</strong></td></tr>';
    }

    if (!empty($object->evaluation_comments)) {
        print '<tr><td class="titlefield">'.$langs->trans("EvaluationComments").'</td>';
        print '<td>'.nl2br($object->evaluation_comments).'</td></tr>';
    }

    if ($object->date_decision) {
        print '<tr><td class="titlefield">'.$langs->trans("DateDecision").'</td>';
        print '<td>'.dol_print_date($object->date_decision, 'dayhour').'</td></tr>';
    }

    if ($object->status == EmployerLoanRequest::STATUS_REJECTED && !empty($object->rejection_reason)) {
        print '<tr><td class="titlefield">'.$langs->trans("RejectionReason").'</td>';
        print '<td>'.nl2br($object->rejection_reason).'</td></tr>';
    }

    print '</table>';
    print '</div>';
}




// End of page
llxFooter();
