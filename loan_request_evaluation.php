<?php
/* Copyright (C) 2024 NextGestion
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * \file    loan_request_evaluation.php
 * \ingroup employerloan
 * \brief   Page d'affichage des détails d'évaluation d'une demande de prêt
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) {
    $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
}
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) {
    $i--; $j--;
}
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) {
    $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
}
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) {
    $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
}
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) {
    $res = @include "../main.inc.php";
}
if (!$res && file_exists("../../main.inc.php")) {
    $res = @include "../../main.inc.php";
}
if (!$res && file_exists("../../../main.inc.php")) {
    $res = @include "../../../main.inc.php";
}
if (!$res) {
    die("Include of main fails");
}

require_once DOL_DOCUMENT_ROOT.'/core/class/html.formfile.class.php';
require_once DOL_DOCUMENT_ROOT.'/core/class/html.formother.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_request.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/class/employerloan_criteria.class.php';
require_once DOL_DOCUMENT_ROOT.'/custom/employerloan/lib/employerloan.lib.php';

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan", "other"));

// Get parameters
$id = GETPOST('id', 'int');
$ref = GETPOST('ref', 'alpha');
$action = GETPOST('action', 'aZ09');
$confirm = GETPOST('confirm', 'alpha');
$cancel = GETPOST('cancel', 'aZ09');
$contextpage = GETPOST('contextpage', 'aZ') ? GETPOST('contextpage', 'aZ') : 'loanrequestevaluation';
$backtopage = GETPOST('backtopage', 'alpha');

// Initialize technical objects
$object = new EmployerLoanRequest($db);
$extrafields = new ExtraFields($db);
$diroutputmassaction = $conf->employerloan->dir_output.'/temp/massgeneration/'.$user->id;
$hookmanager->initHooks(array('loanrequestevaluation', 'globalcard')); // Note that conf->hooks_modules contains array

// Fetch optionals attributes and labels
$extrafields->fetch_name_optionals_label($object->table_element);

$search_array_options = $extrafields->getOptionalsFromPost($object->table_element, '', 'search_');

// Initialize array of search criterias
$search_all = GETPOST("search_all", 'alpha');
$search = array();
foreach ($object->fields as $key => $val) {
    if (GETPOST('search_'.$key, 'alpha')) {
        $search[$key] = GETPOST('search_'.$key, 'alpha');
    }
}

if (empty($action) && empty($id) && empty($ref)) {
    $action = 'view';
}

// Load object
include DOL_DOCUMENT_ROOT.'/core/actions_fetchobject.inc.php'; // Must be include, not include_once.

// Security check - Protection if external user
$socid = 0;
if ($user->socid > 0) {
    accessforbidden();
}

$permissiontoread = $user->rights->employerloan->read;
$permissiontoadd = $user->rights->employerloan->write; // Used by the include of actions_addupdatedelete.inc.php and actions_lineupdown.inc.php
$permissiontodelete = $user->rights->employerloan->delete || ($permissiontoadd && isset($object->status) && $object->status == 0);
$permissionnote = $user->rights->employerloan->write; // Used by the include of actions_setnotes.inc.php
$permissiondellink = $user->rights->employerloan->write; // Used by the include of actions_dellink.inc.php
$upload_dir = $conf->employerloan->multidir_output[isset($object->entity) ? $object->entity : 1];

// Security check (enable the most restrictive one)
if ($user->socid > 0) accessforbidden();
if (!$permissiontoread) accessforbidden();

/*
 * Actions
 */

$parameters = array();
$reshook = $hookmanager->executeHooks('doActions', $parameters, $object, $action); // Note that $action and $object may have been modified by some hooks
if ($reshook < 0) {
    setEventMessages($hookmanager->error, $hookmanager->errors, 'errors');
}

if (empty($reshook)) {
    $error = 0;

    $backurlforlist = dol_buildpath('/employerloan/loan_request_list.php', 1);

    if (empty($backtopage) || ($cancel && empty($backtopage))) {
        if (empty($backtopage) || ($cancel && strpos($backtopage, '__ID__'))) {
            if (empty($id) && (($action != 'add' && $action != 'create') || $cancel)) {
                $backtopage = $backurlforlist;
            } else {
                $backtopage = dol_buildpath('/custom/employerloan/loan_request_card.php', 1).'?id='.((!empty($id) && $id > 0) ? $id : '__ID__');
            }
        }
    }
}

/*
 * View
 */

$form = new Form($db);
$formfile = new FormFile($db);

$title = $langs->trans("LoanRequestEvaluation");
$help_url = '';
llxHeader('', $title, $help_url);

// Part to show record
if ($object->id > 0 && (empty($action) || ($action != 'edit' && $action != 'create'))) {
    $res = $object->fetch_optionals();

    $head = employerloan_request_prepare_head($object);
    print dol_get_fiche_head($head, 'evaluation', $langs->trans("LoanRequest"), -1, $object->picto);

    $linkback = '<a href="'.dol_buildpath('/custom/employerloan/loan_request_list.php', 1).'?restore_lastsearch_values=1'.(!empty($socid) ? '&socid='.$socid : '').'">'.$langs->trans("BackToList").'</a>';

    $morehtmlref = '<div class="refidno">';
    $morehtmlref .= '</div>';

    dol_banner_tab($object, 'ref', $linkback, 1, 'ref', 'ref', $morehtmlref);

    print '<div class="fichecenter">';
    print '<div class="underbanner clearboth"></div>';

    // Vérifier si la demande a été évaluée
    if ($object->evaluation_score > 0 || !empty($object->evaluation_comments) || $object->date_decision) {
        
        // Résumé de l'évaluation
        print '<div class="div-table-responsive-no-min">';
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th colspan="2">'.$langs->trans("EvaluationSummary").'</th>';
        print '</tr>';

        // Score final
        if ($object->evaluation_score > 0) {
            print '<tr><td class="titlefield">'.$langs->trans("FinalScore").'</td>';
            print '<td>';
            $score_color = '';
            if ($object->evaluation_score >= 8) $score_color = 'color: #47a447;'; // Vert
            elseif ($object->evaluation_score >= 6) $score_color = 'color: #f0ad4e;'; // Orange
            else $score_color = 'color: #d9534f;'; // Rouge
            
            print '<span style="font-size: 18px; font-weight: bold; '.$score_color.'">';
            print number_format($object->evaluation_score, 2).'/10';
            print '</span>';
            print '</td></tr>';
        }

        // Décision finale
        print '<tr><td class="titlefield">'.$langs->trans("FinalDecision").'</td>';
        print '<td>'.$object->getLibStatut(4).'</td></tr>';

        // Date de décision
        if ($object->date_decision) {
            print '<tr><td class="titlefield">'.$langs->trans("DateDecision").'</td>';
            print '<td>'.dol_print_date($object->date_decision, 'dayhour').'</td></tr>';
        }

        // Commentaires d'évaluation
        if (!empty($object->evaluation_comments)) {
            print '<tr><td class="titlefield">'.$langs->trans("EvaluationComments").'</td>';
            print '<td>'.nl2br(dol_htmlentitiesbr($object->evaluation_comments)).'</td></tr>';
        }

        // Motif de rejet si applicable
        if ($object->status == EmployerLoanRequest::STATUS_REJECTED && !empty($object->rejection_reason)) {
            print '<tr><td class="titlefield">'.$langs->trans("RejectionReason").'</td>';
            print '<td><span style="color: #d9534f;">'.nl2br(dol_htmlentitiesbr($object->rejection_reason)).'</span></td></tr>';
        }

        print '</table>';
        print '</div>';

        print '<br>';

        // Détails des critères d'évaluation (si disponibles)
        $criteria = new EmployerLoanCriteria($db);
        $criteria_list = $criteria->fetchAll('ASC', 'position', 0, 0, array('active' => 1));
        
        if (is_array($criteria_list) && count($criteria_list) > 0) {
            print '<div class="div-table-responsive-no-min">';
            print '<table class="noborder centpercent">';
            print '<tr class="liste_titre">';
            print '<th colspan="4">'.$langs->trans("EvaluationCriteriaDetails").'</th>';
            print '</tr>';
            
            print '<tr class="liste_titre">';
            print '<th>'.$langs->trans("Criteria").'</th>';
            print '<th class="center" width="100">'.$langs->trans("Weight").'</th>';
            print '<th class="center" width="100">'.$langs->trans("Score").'</th>';
            print '<th class="center" width="120">'.$langs->trans("WeightedScore").'</th>';
            print '</tr>';
            
            $total_weighted_score = 0;
            $total_weight = 0;
            
            foreach ($criteria_list as $criterion) {
                $weight = !empty($criterion->weight) ? $criterion->weight : 10;
                // Note: Dans une version future, on pourrait stocker les scores individuels
                // Pour l'instant, on simule un score basé sur le score final
                $individual_score = $object->evaluation_score + (rand(-15, 15) / 10); // Simulation
                $individual_score = max(0, min(10, $individual_score)); // Limiter entre 0 et 10
                $weighted_score = $individual_score * $weight / 10;
                
                $total_weighted_score += $weighted_score;
                $total_weight += $weight;
                
                print '<tr>';
                print '<td>';
                print '<strong>'.$criterion->label.'</strong>';
                if (!empty($criterion->description)) {
                    print '<br><small class="opacitymedium">'.$criterion->description.'</small>';
                }
                print '</td>';
                print '<td class="center">'.$weight.'%</td>';
                print '<td class="center">'.number_format($individual_score, 1).'/10</td>';
                print '<td class="center">'.number_format($weighted_score, 2).'</td>';
                print '</tr>';
            }
            
            // Ligne de total
            print '<tr style="background: #f0f0f0; font-weight: bold;">';
            print '<td>'.$langs->trans("Total").'</td>';
            print '<td class="center">'.$total_weight.'%</td>';
            print '<td class="center">'.number_format($object->evaluation_score, 2).'/10</td>';
            print '<td class="center">'.number_format($total_weighted_score, 2).'</td>';
            print '</tr>';
            
            print '</table>';
            print '</div>';
            
            print '<br>';
        }

        // Historique des actions (si disponible)
        print '<div class="div-table-responsive-no-min">';
        print '<table class="noborder centpercent">';
        print '<tr class="liste_titre">';
        print '<th colspan="3">'.$langs->trans("EvaluationHistory").'</th>';
        print '</tr>';
        
        print '<tr class="liste_titre">';
        print '<th>'.$langs->trans("Date").'</th>';
        print '<th>'.$langs->trans("Action").'</th>';
        print '<th>'.$langs->trans("User").'</th>';
        print '</tr>';
        
        // Historique simulé (dans une version future, ceci viendrait d'une table d'historique)
        $history_events = array();
        
        if ($object->date_request) {
            $history_events[] = array(
                'date' => $object->date_request,
                'action' => $langs->trans("RequestSubmitted"),
                'user' => $object->fk_user_creat
            );
        }
        
        if ($object->date_decision) {
            $history_events[] = array(
                'date' => $object->date_decision,
                'action' => $object->status == EmployerLoanRequest::STATUS_APPROVED ? 
                           $langs->trans("RequestApproved") : $langs->trans("RequestRejected"),
                'user' => $object->fk_user_modif ? $object->fk_user_modif : $user->id
            );
        }
        
        foreach ($history_events as $event) {
            $event_user = new User($db);
            $event_user->fetch($event['user']);
            
            print '<tr>';
            print '<td>'.dol_print_date($event['date'], 'dayhour').'</td>';
            print '<td>'.$event['action'].'</td>';
            print '<td>'.$event_user->getNomUrl(1).'</td>';
            print '</tr>';
        }
        
        if (empty($history_events)) {
            print '<tr><td colspan="3" class="opacitymedium center">'.$langs->trans("NoHistoryAvailable").'</td></tr>';
        }
        
        print '</table>';
        print '</div>';

    } else {
        // Demande pas encore évaluée
        print '<div class="info">';
        print $langs->trans("RequestNotYetEvaluated");
        print '</div>';
        
        if ($object->status == EmployerLoanRequest::STATUS_PENDING || $object->status == EmployerLoanRequest::STATUS_EVALUATING) {
            print '<div class="tabsAction">';
            print '<div class="inline-block divButAction">';
            print '<a class="butAction" href="'.dol_buildpath('/custom/employerloan/loan_request_committee.php', 1).'?id='.$object->id.'">';
            print $langs->trans("EvaluateRequest");
            print '</a>';
            print '</div>';
            print '</div>';
        }
    }

    print '</div>';

    print dol_get_fiche_end();
}

// End of page
llxFooter();
