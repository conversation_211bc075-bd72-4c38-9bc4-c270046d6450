<?php
/**
 * Debug des structures de tables
 */

require_once '../../main.inc.php';

// Vérification des permissions
if (!$user->admin) {
    accessforbidden();
}

print '<html><head><title>Debug tables</title></head><body>';
print '<h1>Debug des structures de tables</h1>';

$tables = [
    'employer_loan_schedule',
    'employer_loan_installment',
    'employer_loan',
    'employer_payment_loan'
];

foreach ($tables as $table) {
    print '<h3>Table: '.$table.'</h3>';
    
    $sql_desc = "DESCRIBE ".MAIN_DB_PREFIX.$table;
    $resql_desc = $db->query($sql_desc);
    if ($resql_desc) {
        print '<table border="1" style="border-collapse: collapse; margin-bottom: 20px;">';
        print '<tr style="background: #f0f0f0;"><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>';
        while ($obj = $db->fetch_object($resql_desc)) {
            print '<tr>';
            print '<td>'.$obj->Field.'</td>';
            print '<td>'.$obj->Type.'</td>';
            print '<td>'.$obj->Null.'</td>';
            print '<td>'.$obj->Key.'</td>';
            print '<td>'.$obj->Default.'</td>';
            print '</tr>';
        }
        print '</table>';
    } else {
        print '<p style="color: red;">Table '.$table.' n\'existe pas ou erreur : '.$db->lasterror().'</p>';
    }
}

print '</body></html>';
