<?php
/**
 * Script de test des permissions pour le module employerloan
 */

// Load Dolibarr environment
$res = 0;
// Try main.inc.php into web root known defined into CONTEXT_DOCUMENT_ROOT (not always defined)
if (!$res && !empty($_SERVER["CONTEXT_DOCUMENT_ROOT"])) $res = @include $_SERVER["CONTEXT_DOCUMENT_ROOT"]."/main.inc.php";
// Try main.inc.php into web root detected using web root calculated from SCRIPT_FILENAME
$tmp = empty($_SERVER['SCRIPT_FILENAME']) ? '' : $_SERVER['SCRIPT_FILENAME']; $tmp2 = realpath(__FILE__); $i = strlen($tmp) - 1; $j = strlen($tmp2) - 1;
while ($i > 0 && $j > 0 && isset($tmp[$i]) && isset($tmp2[$j]) && $tmp[$i] == $tmp2[$j]) { $i--; $j--; }
if (!$res && $i > 0 && file_exists(substr($tmp, 0, ($i + 1))."/main.inc.php")) $res = @include substr($tmp, 0, ($i + 1))."/main.inc.php";
if (!$res && $i > 0 && file_exists(dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php")) $res = @include dirname(substr($tmp, 0, ($i + 1)))."/main.inc.php";
// Try main.inc.php using relative path
if (!$res && file_exists("../main.inc.php")) $res = @include "../main.inc.php";
if (!$res && file_exists("../../main.inc.php")) $res = @include "../../main.inc.php";
if (!$res) die("Include of main fails");

// Load translation files required by the page
$langs->loadLangs(array("employerloan@employerloan"));

$title = "Test des permissions EmployerLoan";
llxHeader("", $title);

print '<h1>'.$title.'</h1>';

print '<h2>Informations utilisateur</h2>';
print '<p><strong>Utilisateur connecté :</strong> '.$user->login.' ('.$user->firstname.' '.$user->lastname.')</p>';
print '<p><strong>ID utilisateur :</strong> '.$user->id.'</p>';

print '<h2>Vérification des permissions employerloan</h2>';

// Vérifier si le module est activé
if (isModEnabled('employerloan')) {
    print '<p>✅ Module employerloan activé</p>';
} else {
    print '<p>❌ Module employerloan non activé</p>';
}

// Vérifier les permissions
if (isset($user->rights->employerloan)) {
    print '<p>✅ Droits employerloan définis</p>';
    
    if (isset($user->rights->employerloan->read)) {
        if ($user->rights->employerloan->read) {
            print '<p>✅ Droit de lecture : OUI</p>';
        } else {
            print '<p>❌ Droit de lecture : NON</p>';
        }
    } else {
        print '<p>❌ Droit de lecture non défini</p>';
    }
    
    if (isset($user->rights->employerloan->write)) {
        if ($user->rights->employerloan->write) {
            print '<p>✅ Droit d\'écriture : OUI</p>';
        } else {
            print '<p>❌ Droit d\'écriture : NON</p>';
        }
    } else {
        print '<p>❌ Droit d\'écriture non défini</p>';
    }
    
    if (isset($user->rights->employerloan->delete)) {
        if ($user->rights->employerloan->delete) {
            print '<p>✅ Droit de suppression : OUI</p>';
        } else {
            print '<p>❌ Droit de suppression : NON</p>';
        }
    } else {
        print '<p>❌ Droit de suppression non défini</p>';
    }
} else {
    print '<p>❌ Droits employerloan non définis</p>';
}

print '<h2>Test d\'accès aux pages</h2>';

// Test restrictedArea
try {
    $result = restrictedArea($user, 'employerloan', '', '', '');
    print '<p>✅ restrictedArea(employerloan) : OK</p>';
} catch (Exception $e) {
    print '<p>❌ restrictedArea(employerloan) : '.$e->getMessage().'</p>';
}

// Test hasRight
if ($user->hasRight('employerloan', 'read')) {
    print '<p>✅ hasRight(employerloan, read) : OK</p>';
} else {
    print '<p>❌ hasRight(employerloan, read) : NON</p>';
}

if ($user->hasRight('employerloan', 'write')) {
    print '<p>✅ hasRight(employerloan, write) : OK</p>';
} else {
    print '<p>❌ hasRight(employerloan, write) : NON</p>';
}

print '<h2>Actions recommandées</h2>';

if (!isset($user->rights->employerloan) || !$user->rights->employerloan->read) {
    print '<div class="warning">';
    print '<p><strong>⚠️ Permissions manquantes</strong></p>';
    print '<p>L\'utilisateur <strong>'.$user->login.'</strong> n\'a pas les permissions nécessaires pour accéder au module employerloan.</p>';
    print '<p><strong>Solution :</strong></p>';
    print '<ol>';
    print '<li>Aller dans le menu <strong>Accueil → Utilisateurs</strong></li>';
    print '<li>Modifier l\'utilisateur <strong>'.$user->login.'</strong></li>';
    print '<li>Dans l\'onglet <strong>Permissions</strong>, cocher les droits pour le module <strong>Crédits salariés</strong> :</li>';
    print '<ul>';
    print '<li>☑️ Lire les crédits salariés</li>';
    print '<li>☑️ Créer/modifier les crédits salariés</li>';
    print '<li>☑️ Supprimer les crédits salariés (optionnel)</li>';
    print '</ul>';
    print '<li>Sauvegarder les modifications</li>';
    print '<li>Se déconnecter et se reconnecter</li>';
    print '</ol>';
    print '</div>';
} else {
    print '<div class="ok">';
    print '<p>✅ <strong>Permissions OK</strong></p>';
    print '<p>L\'utilisateur a les permissions nécessaires. Vous pouvez maintenant accéder aux pages :</p>';
    print '<ul>';
    print '<li><a href="list.php">Liste des prêts</a></li>';
    print '<li><a href="card.php?action=create">Nouveau prêt</a></li>';
    print '</ul>';
    print '</div>';
}

llxFooter();
